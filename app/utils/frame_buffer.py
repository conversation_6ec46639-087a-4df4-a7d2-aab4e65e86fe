import logging
import threading
import numpy as np
from typing import List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class FrameInfo:
    """视频帧信息"""
    frame: np.ndarray      # 帧数据
    timestamp: float       # 时间戳（毫秒）
    frame_index: int       # 帧序号（用于调试和日志）

class FrameBuffer:
    """视频帧缓存"""
    def __init__(self, buffer_duration: int):
        """
        初始化帧缓存
        :param buffer_duration: 缓存时长（秒）
        """
        self.buffer_duration = buffer_duration
        self.frames: List[FrameInfo] = []
        self.frame_count = 0
        self._lock = threading.Lock()  # 用于线程安全

    def add_frame(self, frame: np.ndarray, timestamp: float) -> None:
        """
        添加新帧到缓存
        :param frame: 视频帧
        :param timestamp: 时间戳（毫秒）
        """
        with self._lock:
            self.frame_count += 1
            frame_info = FrameInfo(
                frame=frame.copy(),
                timestamp=timestamp,
                frame_index=self.frame_count
            )
            self.frames.append(frame_info)
            self._clean_expired_frames(timestamp)

    def _clean_expired_frames(self, current_timestamp: float) -> None:
        """
        清理过期帧
        :param current_timestamp: 当前时间戳（毫秒）
        """
        expire_before = current_timestamp - (self.buffer_duration * 1000)
        original_count = len(self.frames)
        self.frames = [f for f in self.frames if f.timestamp >= expire_before]
        removed_count = original_count - len(self.frames)
        
        # 如果有帧被清理，记录日志
        # if removed_count > 0:
        #     logger.debug(f"清理过期帧: 清理前={original_count}, 清理后={len(self.frames)}, 清理数量={removed_count}, 过期时间戳={expire_before}")

    def get_frames_around_timestamp(self, target_timestamp: float, duration: float) -> List[FrameInfo]:
        """
        获取指定时间戳前后指定时长的帧
        :param target_timestamp: 目标时间戳（毫秒）
        :param duration: 前后时长（秒）
        :return: 帧列表
        """
        with self._lock:
            start_time = target_timestamp - (duration * 1000)
            end_time = target_timestamp + (duration * 1000)
            return [f for f in self.frames if start_time <= f.timestamp <= end_time]
            
    def get_frames_before_timestamp(self, target_timestamp: float, duration: float) -> List[FrameInfo]:
        """
        获取指定时间戳前面指定时长的帧
        :param target_timestamp: 目标时间戳（毫秒）
        :param duration: 前面的时长（秒）
        :return: 帧列表
        """
        with self._lock:
            start_time = target_timestamp - (duration * 1000)
            end_time = target_timestamp
            
            # 记录查询的时间范围
            logger.info(f"获取帧范围: 开始时间={start_time}, 结束时间={end_time}, 目标持续时间={duration}秒")
            logger.info(f"当前缓存状态: 总帧数={self.frame_count}, 缓存帧数={len(self.frames)}")
            
            # 如果缓存中有帧，记录缓存的时间范围
            if self.frames:
                earliest_frame = min(self.frames, key=lambda f: f.timestamp)
                latest_frame = max(self.frames, key=lambda f: f.timestamp)
                logger.info(f"缓存时间范围: 最早={earliest_frame.timestamp}, 最晚={latest_frame.timestamp}, 跨度={(latest_frame.timestamp - earliest_frame.timestamp) / 1000.0:.2f}秒")
            
            frames = [f for f in self.frames if start_time <= f.timestamp <= end_time]
            
            # 记录找到的帧数量
            logger.info(f"找到符合条件的帧: {len(frames)}帧, 占缓存总帧数的{len(frames) / len(self.frames) * 100 if self.frames else 0:.2f}%")
            
            return frames 