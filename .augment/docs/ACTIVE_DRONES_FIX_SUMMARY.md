# 活跃无人机获取逻辑修复总结

## 问题分析

通过分析代码，发现了活跃无人机获取逻辑的几个关键问题：

### 1. 调用链路混乱
- `DroneInfoManager.get_active_drones()` 直接调用 `shared_state_manager.get_active_drone_ids()`
- `WebSocketClient.get_active_drone_ids()` 也调用 `shared_state_manager.get_active_drone_ids()`
- 但是 `shared_state_manager.get_active_drones()` 中的过滤逻辑有问题

### 2. 状态更新逻辑断裂
- WebSocket 客户端在 `_process_drone_status()` 中更新状态
- DroneInfoManager 在 `_update_drone_status()` 中也更新状态
- 两者都更新同一个 `active_drones` 字典，但逻辑不完全一致

### 3. 配置过滤逻辑不一致
- `shared_state_manager.get_active_drones()` 中有配置过滤
- 但在 `StreamMonitor._monitor_loop()` 中又重复进行了配置过滤
- 配置文件中的 `DRONE_LIST` 返回的是字典列表，但过滤时用字符串比较

## 修复方案

### 1. 重构 SharedStateManager 的活跃无人机检测逻辑

**修改文件**: `app/utils/shared_state.py`

**主要改动**:
- 为 `get_active_drones()` 和 `get_active_drone_ids()` 方法添加 `apply_config_filter` 参数
- 修复配置过滤逻辑，使用 `config.get_drone_codes()` 获取正确的无人机代码列表
- 改进缓存机制，支持不同过滤参数的独立缓存
- 增加详细的调试日志，便于问题排查

**关键代码**:
```python
def get_active_drones(self, force_refresh: bool = False, apply_config_filter: bool = True) -> Dict[str, Dict[str, Any]]:
    # 生成缓存键，包含配置过滤参数
    cache_key = f"active_drones_{apply_config_filter}"
    
    # 4. 配置过滤（只监控配置文件中指定的无人机）
    if apply_config_filter and config.stream_monitor.only_monitor_config_drones:
        config_drone_codes = config.get_drone_codes()
        final_drones = {
            drone_code: status 
            for drone_code, status in filtered_drones.items() 
            if drone_code in config_drone_codes
        }
```

### 2. 统一活跃无人机状态更新机制

**修改文件**: 
- `app/database/drone_info.py`
- `app/database/websocket_client.py`

**主要改动**:
- 确保 WebSocket 客户端和 DroneInfoManager 都正确更新 `active_drones` 状态
- 修复 DroneInfoManager 中的配置检查逻辑，使其与 SharedStateManager 保持一致
- WebSocket 客户端不应用配置过滤，返回所有活跃的无人机
- DroneInfoManager 应用配置过滤，用于业务逻辑

### 3. 修复配置过滤逻辑

**修改文件**: 
- `app/processor/stream_processor.py`
- `app/services/monitor_service.py`

**主要改动**:
- 移除 StreamMonitor 和 MonitorService 中的重复配置过滤逻辑
- 统一使用 `shared_state_manager.get_active_drone_ids(apply_config_filter=True)` 获取已过滤的活跃无人机列表
- 简化代码逻辑，避免重复过滤

## 测试验证

创建了测试脚本 `test_shared_state_only.py` 来验证修复效果：

### 测试场景
1. **不应用配置过滤**: 返回所有活跃的无人机（包括不在配置列表中的）
2. **应用配置过滤**: 只返回配置列表中的活跃无人机
3. **状态更新**: 验证状态更新和缓存机制正常工作
4. **单个无人机检查**: 验证 `is_drone_active()` 方法正常工作

### 测试结果
✅ 所有测试通过，功能正常工作

## 修复效果

### 修复前的问题
- `get_active_drones()` 方法永远返回空列表
- 配置过滤逻辑错误，无法正确识别配置中的无人机
- 调用链路混乱，不同组件获取的结果不一致

### 修复后的效果
- ✅ 正确获取活跃无人机列表
- ✅ 配置过滤逻辑正常工作
- ✅ WebSocket 数据和视频流状态检查逻辑统一
- ✅ 支持灵活的过滤参数控制
- ✅ 缓存机制优化，提高性能
- ✅ 详细的调试日志，便于问题排查

## 使用说明

### 获取所有活跃无人机（不过滤）
```python
from app.utils.shared_state import shared_state_manager
active_drones = shared_state_manager.get_active_drone_ids(apply_config_filter=False)
```

### 获取配置中的活跃无人机（过滤）
```python
from app.utils.shared_state import shared_state_manager
active_drones = shared_state_manager.get_active_drone_ids(apply_config_filter=True)
```

### 检查单个无人机是否活跃
```python
from app.utils.shared_state import shared_state_manager
is_active = shared_state_manager.is_drone_active('drone_code')
```

## 注意事项

1. **向后兼容**: 所有修改都保持向后兼容，现有代码无需修改
2. **配置依赖**: 确保 `config.yaml` 中的 `drone_list` 配置正确
3. **性能优化**: 使用缓存机制，避免频繁的状态检查
4. **调试支持**: 增加了详细的调试日志，可通过设置日志级别为 DEBUG 查看详细信息

## 相关文件

- `app/utils/shared_state.py` - 核心状态管理
- `app/database/drone_info.py` - 无人机信息管理
- `app/database/websocket_client.py` - WebSocket 客户端
- `app/processor/stream_processor.py` - 流处理器
- `app/services/monitor_service.py` - 监控服务
- `test_shared_state_only.py` - 测试脚本
