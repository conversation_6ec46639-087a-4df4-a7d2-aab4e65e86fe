#!/usr/bin/env python
"""
专门调试ImageProcessor测试的脚本
"""
import os
import sys
import pytest
import logging
import importlib
import inspect

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

# 确保使用config_fake.yaml作为配置文件
os.environ['CONFIG_FILE'] = 'config.yaml'
logger.info(f"使用配置文件: {os.environ['CONFIG_FILE']}")

def check_image_processor_module():
    """检查ImageProcessor模块"""
    logger.info("检查ImageProcessor模块...")
    
    try:
        from app.processor import image_processor
        logger.info(f"成功导入image_processor模块，路径: {image_processor.__file__}")
        
        # 检查ImageProcessor类
        logger.info(f"ImageProcessor类: {image_processor.ImagePreprocessor}")
        
        # 检查PreprocessMethod枚举
        logger.info(f"PreprocessMethod枚举: {image_processor.PreprocessMethod}")
        
        # 尝试创建ImageProcessor实例
        try:
            from unittest.mock import MagicMock, patch
            with patch('app.processor.image_processor.RealESRGANer'), \
                 patch('app.processor.image_processor.RRDBNet'):
                processor = image_processor.ImagePreprocessor()
                logger.info(f"成功创建ImageProcessor实例: {processor}")
                logger.info(f"preprocess属性: {processor.preprocess}, 类型: {type(processor.preprocess)}")
                logger.info(f"target_size属性: {processor.target_size}")
        except Exception as e:
            logger.error(f"创建ImageProcessor实例失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    except ImportError as e:
        logger.error(f"导入image_processor模块失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def check_test_file():
    """检查测试文件"""
    logger.info("检查测试文件...")
    
    test_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                             'unit', 'app', 'processor', 'test_image_processor.py')
    
    if not os.path.exists(test_file):
        logger.error(f"测试文件不存在: {test_file}")
        return
    
    logger.info(f"测试文件存在: {test_file}")
    
    try:
        # 导入测试模块
        spec = importlib.util.spec_from_file_location("test_image_processor", test_file)
        test_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        logger.info("成功导入测试模块")
        
        # 查找测试函数
        test_functions = [name for name, obj in inspect.getmembers(test_module) 
                         if name.startswith('test_') and callable(obj)]
        logger.info(f"找到测试函数: {test_functions}")
        
        # 查找fixture
        fixtures = [name for name, obj in inspect.getmembers(test_module) 
                   if hasattr(obj, '_pytestfixturefunction')]
        logger.info(f"找到fixture: {fixtures}")
    except Exception as e:
        logger.error(f"导入测试模块失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def run_specific_test():
    """运行特定测试"""
    logger.info("运行特定测试...")
    
    # 获取测试文件路径
    test_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                            'unit', 'app', 'processor', 'test_image_processor.py')
    
    # 运行test_init测试
    logger.info("运行test_init测试...")
    result = pytest.main(["-xvs", f"{test_file}::test_init"])
    logger.info(f"test_init测试结果: {result}")

if __name__ == "__main__":
    logger.info("开始调试ImageProcessor测试...")
    
    # 检查模块
    check_image_processor_module()
    
    # 检查测试文件
    check_test_file()
    
    # 运行特定测试
    run_specific_test()
    
    logger.info("调试完成") 