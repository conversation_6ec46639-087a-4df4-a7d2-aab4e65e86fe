import os
import platform
import subprocess
import logging
from typing import Dict
from app.utils.config import config
import time

logger = logging.getLogger(__name__)

class NFSManager:
    def __init__(self):
        """初始化NFS管理器"""
        self.nfs_server = config.NFS_SERVER
        self.remote_paths = config.NFS_REMOTE_PATHS
        self.mount_points = self._get_mount_points()
        self.is_windows = platform.system().lower() == 'windows'
        
    def _get_mount_points(self) -> Dict[str, str]:
        """根据运行模式获取挂载点配置"""
        mode = 'dev' if config.IS_DEV_MODE else 'prod'
        mount_points = config.config_data['storage']['mount_points'][mode]
        
        # 确保所有路径都是绝对路径
        if mode == 'dev':
            # 开发模式下，将相对路径转换为绝对路径
            base_dir = os.path.abspath(mount_points['base'])
            mount_points['base'] = base_dir
            mount_points['image'] = os.path.join(base_dir, 'imageFile')
            mount_points['video'] = os.path.join(base_dir, 'videoFile')
        
        return mount_points
        
    def _is_mounted(self, mount_point: str) -> bool:
        """
        检查指定路径是否已经挂载
        :param mount_point: 挂载点路径
        :return: 是否已挂载
        """
        try:
            if self.is_windows:
                # Windows系统下，只检查目录是否存在且可访问
                return os.path.exists(mount_point) and os.access(mount_point, os.R_OK)
            else:
                # Linux系统下使用mount命令检查
                result = subprocess.run(['mount'], capture_output=True, text=True)
                return mount_point in result.stdout
        except Exception as e:
            logger.error(f"检查挂载状态失败: {str(e)}")
            return False
            
    def _create_mount_point(self, mount_point: str) -> bool:
        """
        创建挂载点目录
        :param mount_point: 挂载点路径
        :return: 是否创建成功
        """
        try:
            os.makedirs(mount_point, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建挂载点目录失败: {str(e)}")
            return False
            
    def _check_write_permission(self, mount_point: str) -> bool:
        """检查挂载点是否有写入权限"""
        try:
            test_file = os.path.join(mount_point, '.write_test')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return True
        except Exception as e:
            logger.error(f"挂载点 {mount_point} 写入权限检查失败: {str(e)}")
            return False

    def _check_network_connection(self) -> bool:
        """测试与NFS服务器的网络连接"""
        try:
            # 使用ping命令测试连接
            cmd = ['ping', '-c', '1', self.nfs_server] if not self.is_windows else ['ping', '-n', '1', self.nfs_server]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"网络连接正常: {self.nfs_server}")
                return True
            else:
                logger.error(f"无法连接到NFS服务器: {self.nfs_server}")
                return False
        except Exception as e:
            logger.error(f"网络连接测试失败: {str(e)}")
            return False

    def _restart_rpc_services(self) -> bool:
        """重启RPC相关服务"""
        try:
            # 停止服务
            subprocess.run(['sudo', 'systemctl', 'stop', 'rpcbind.socket'], capture_output=True, text=True)
            subprocess.run(['sudo', 'systemctl', 'stop', 'rpcbind.service'], capture_output=True, text=True)
            
            # 等待服务完全停止
            time.sleep(2)
            
            # 启动服务
            subprocess.run(['sudo', 'systemctl', 'start', 'rpcbind.socket'], capture_output=True, text=True)
            subprocess.run(['sudo', 'systemctl', 'start', 'rpcbind.service'], capture_output=True, text=True)
            
            # 重启NFS客户端
            subprocess.run(['sudo', 'systemctl', 'restart', 'nfs-client.target'], capture_output=True, text=True)
            
            # 等待服务启动
            time.sleep(2)
            
            # 验证服务状态
            result = subprocess.run(['rpcinfo', '-p', 'localhost'], capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            logger.error(f"重启RPC服务失败: {str(e)}")
            return False

    def _check_nfs_service(self) -> bool:
        """检查NFS服务是否正常运行"""
        try:
            if not self.is_windows:
                # 检查rpcbind服务状态
                logger.info("检查rpcbind服务状态...")
                rpcbind_cmd = ['systemctl', 'status', 'rpcbind']
                rpcbind_result = subprocess.run(rpcbind_cmd, capture_output=True, text=True)
                
                if rpcbind_result.returncode != 0:
                    logger.warning("rpcbind服务异常，尝试重启服务...")
                    if not self._restart_rpc_services():
                        logger.error("重启RPC服务失败")
                        return False
                
                # 检查本地RPC服务
                logger.info("检查本地RPC服务...")
                rpcinfo_local_cmd = ['rpcinfo', '-p', 'localhost']
                rpcinfo_local_result = subprocess.run(rpcinfo_local_cmd, capture_output=True, text=True)
                logger.debug(f"本地RPC服务: {rpcinfo_local_result.stdout}")
                
                # 检查远程RPC服务 - 记录信息但不影响结果
                logger.info(f"检查远程RPC服务 ({self.nfs_server})...")
                try:
                    rpcinfo_remote_cmd = ['rpcinfo', '-p', self.nfs_server]
                    rpcinfo_remote_result = subprocess.run(rpcinfo_remote_cmd, capture_output=True, text=True)
                    logger.debug(f"远程RPC服务: {rpcinfo_remote_result.stdout}")
                except Exception as e:
                    logger.warning(f"远程RPC检查失败，但这可能不影响NFS挂载: {str(e)}")
                
                # 移除showmount检查，因为它不是必需的
                # 只要本地rpcbind服务正常运行就返回True
                return True
                
            return True  # Windows下暂时返回True
        except Exception as e:
            logger.error(f"NFS服务检查失败: {str(e)}")
            return False

    def _mount_nfs(self, remote_path: str, mount_point: str) -> bool:
        """挂载NFS共享"""
        try:
            # 1. 先检查网络连接
            if not self._check_network_connection():
                return False
                
            # 2. 检查NFS服务
            if not self._check_nfs_service():
                return False
                
            # 确保挂载点目录存在
            if not self._create_mount_point(mount_point):
                return False
                
            # 如果已经挂载，检查写入权限
            if self._is_mounted(mount_point):
                if self._check_write_permission(mount_point):
                    logger.info(f"NFS已经挂载到 {mount_point} 且具有写入权限")
                    return True
                else:
                    logger.warning(f"NFS已挂载但无写入权限，尝试重新挂载: {mount_point}")
                    self._unmount_nfs(mount_point)
            
            if self.is_windows:
                # Windows系统下的处理
                windows_path = remote_path.replace('/', '\\')
                remote_unc = f"\\\\{self.nfs_server}\\{windows_path}"
                
                # 先尝试清理可能存在的连接
                cleanup_cmd = ['net', 'use', mount_point, '/delete']
                try:
                    subprocess.run(cleanup_cmd, capture_output=True, text=True)
                except Exception:
                    pass

                # Windows下的挂载命令
                cmd = [
                    'net', 'use', 
                    mount_point, 
                    remote_unc,
                    '/PERSISTENT:YES',
                    '/USER:*',
                ]
            else:
                # Linux系统下的挂载命令 - 简化为已知可用的配置
                cmd = [
                    'sudo',  # sudo作为单独的命令
                    'mount',
                    f'{self.nfs_server}:{remote_path}',
                    mount_point
                ]
            
            # 添加调试信息
            logger.info(f"NFS服务器: {self.nfs_server}")
            logger.info(f"远程路径: {remote_path}")
            logger.info(f"挂载点: {mount_point}")
            logger.info(f"完整挂载命令: {' '.join(cmd)}")
            
            # 执行挂载命令
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # 验证写入权限
                if self._check_write_permission(mount_point):
                    logger.info(f"NFS挂载成功且具有写入权限: {mount_point}")
                    return True
                else:
                    logger.error(f"NFS挂载成功但无写入权限: {mount_point}")
                    return False
            else:
                logger.error(f"NFS挂载失败: {result.stderr}")
                logger.error(f"挂载命令: {' '.join(cmd)}")
                logger.error(f"返回码: {result.returncode}")
                logger.error(f"标准输出: {result.stdout}")
                logger.error(f"标准错误: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"NFS挂载出错: {str(e)}")
            return False
            
    def _unmount_nfs(self, mount_point: str) -> bool:
        """
        卸载NFS挂载点
        :param mount_point: 挂载点路径
        :return: 是否卸载成功
        """
        try:
            if not self._is_mounted(mount_point):
                logger.info(f"NFS未挂载到 {mount_point}")
                return True
                
            if self.is_windows:
                # Windows系统下，使用net use命令
                cmd = ['net', 'use', mount_point, '/delete']
            else:
                # Linux系统下使用umount命令
                cmd = ['sudo', 'umount', mount_point]  # 同样添加sudo
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"NFS卸载成功: {mount_point}")
                return True
            else:
                logger.error(f"NFS卸载失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"NFS卸载出错: {str(e)}")
            return False
            
    def setup(self) -> bool:
        """
        设置所有NFS挂载点
        :return: 是否全部设置成功
        """
        success = True
        
        # 直接挂载子目录，不再尝试挂载基础目录
        # 挂载图片目录
        if not self._mount_nfs(
            f"{self.remote_paths['base']}/imageFile",  # 直接拼接完整路径
            self.mount_points['image']
        ):
            logger.error("图片目录挂载失败")
            success = False
            
        # 挂载视频目录
        if not self._mount_nfs(
            f"{self.remote_paths['base']}/videoFile",  # 直接拼接完整路径
            self.mount_points['video']
        ):
            logger.error("视频目录挂载失败")
            success = False
            
        # 跳过报告目录的NFS挂载，使用本地存储
        logger.info("报告目录使用本地存储，跳过NFS挂载")
            
        if not success:
            logger.error("部分目录挂载失败，请检查配置和权限")
            
        return success
        
    def check_and_remount(self) -> bool:
        """
        检查所有挂载点状态，必要时重新挂载
        :return: 是否全部挂载正常
        """
        success = True
        
        # 检查图片目录
        if not self._is_mounted(self.mount_points['image']):
            logger.warning(f"图片目录未挂载，尝试重新挂载: {self.mount_points['image']}")
            if not self._mount_nfs(
                f"{self.remote_paths['base']}/imageFile",
                self.mount_points['image']
            ):
                logger.error("图片目录重新挂载失败")
                success = False
                
        # 检查视频目录
        if not self._is_mounted(self.mount_points['video']):
            logger.warning(f"视频目录未挂载，尝试重新挂载: {self.mount_points['video']}")
            if not self._mount_nfs(
                f"{self.remote_paths['base']}/videoFile",
                self.mount_points['video']
            ):
                logger.error("视频目录重新挂载失败")
                success = False
                
        # 跳过报告目录的检查，使用本地存储
        logger.debug("报告目录使用本地存储，跳过NFS挂载检查")
                
        return success
        
    def cleanup(self) -> bool:
        """
        清理所有NFS挂载点
        :return: 是否全部清理成功
        """
        success = True
        
        # 先卸载子目录
        # 卸载图片目录
        if not self._unmount_nfs(self.mount_points['image']):
            logger.error("图片目录卸载失败")
            success = False
            
        # 卸载视频目录
        if not self._unmount_nfs(self.mount_points['video']):
            logger.error("视频目录卸载失败")
            success = False
            
        # 卸载报告目录
        if not self._unmount_nfs(self.mount_points['reports']):
            logger.error("报告目录卸载失败")
            success = False
            
        return success

# 创建全局实例
nfs_manager = NFSManager() 