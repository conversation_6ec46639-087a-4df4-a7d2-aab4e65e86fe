"""
共享状态模块
用于存储应用程序中需要在多个模块之间共享的状态
"""
import json
import os
import time
import logging
from typing import Dict, Any, Optional, List
from threading import Lock

logger = logging.getLogger(__name__)

class SharedStateManager:
    """共享状态管理器"""
    
    def __init__(self):
        """初始化共享状态管理器"""
        self._lock = Lock()
        self.status_file_path = None
        self.last_save_time = 0
        self.save_interval = 30  # 保存间隔（秒）
        self.cleanup_interval = 60  # 清理间隔（秒）
        self.last_cleanup_time = 0
        self.status_timeout = 300  # 状态超时时间（秒）
        
        # 缓存机制
        self._cache_result = {}
        self._cache_time = 0
        self._cache_ttl = 10  # 缓存10秒
        
    def set_status_file_path(self, file_path: str):
        """设置状态文件路径"""
        self.status_file_path = file_path
        
    def get_active_drones(self, force_refresh: bool = False) -> Dict[str, Dict[str, Any]]:
        """
        获取活跃无人机状态（线程安全，带统一检测逻辑）
        :param force_refresh: 是否强制刷新缓存
        :return: 活跃无人机状态字典
        """
        current_time = time.time()
        
        # 检查缓存
        if (not force_refresh and 
            current_time - self._cache_time < self._cache_ttl and 
            self._cache_result):
            logger.debug(f"使用缓存的活跃无人机数据，缓存时间: {current_time - self._cache_time:.1f}秒前")
            return self._cache_result.copy()
        
        with self._lock:
            try:
                # 导入配置（延迟导入避免循环依赖）
                from app.utils.config import config
                
                filtered_drones = {}
                
                for drone_code, status in active_drones.items():
                    # 1. 检查是否标记为活跃
                    if not status.get('is_active', False):
                        continue
                        
                    # 2. 检查时效性（5分钟超时）
                    last_update = status.get('last_update', 0)
                    if current_time - last_update > 300:
                        logger.debug(f"无人机 {drone_code} 状态已过期，跳过")
                        continue
                        
                    # 3. 检查模式代码是否在活跃列表中
                    mode_code = status.get('mode_code')
                    if mode_code is not None and mode_code not in config.WEBSOCKET_ACTIVE_MODE_CODES:
                        logger.debug(f"无人机 {drone_code} 模式代码 {mode_code} 不在活跃列表中")
                        continue
                    
                    # 通过所有检查，添加到结果中
                    filtered_drones[drone_code] = status
                
                # 4. 配置过滤（只监控配置文件中指定的无人机）
                if config.stream_monitor.only_monitor_config_drones:
                    config_drone_list = config.DRONE_LIST
                    final_drones = {
                        drone_code: status 
                        for drone_code, status in filtered_drones.items() 
                        if drone_code in config_drone_list
                    }
                    logger.debug(f"配置过滤: 过滤前 {len(filtered_drones)} 个，过滤后 {len(final_drones)} 个")
                else:
                    final_drones = filtered_drones
                
                # 更新缓存
                self._cache_result = final_drones.copy()
                self._cache_time = current_time
                
                logger.debug(f"统一检测完成，活跃无人机: {len(final_drones)} 个")
                return final_drones
                
            except Exception as e:
                logger.error(f"获取活跃无人机状态失败: {str(e)}")
                # 发生错误时返回原始数据的副本
                return active_drones.copy()
    
    def get_active_drone_ids(self, force_refresh: bool = False) -> List[str]:
        """
        获取活跃无人机ID列表
        :param force_refresh: 是否强制刷新缓存
        :return: 活跃无人机ID列表
        """
        active_data = self.get_active_drones(force_refresh)
        return list(active_data.keys())
    
    def is_drone_active(self, drone_code: str) -> bool:
        """
        检查指定无人机是否活跃
        :param drone_code: 无人机编号
        :return: 是否活跃
        """
        active_drone_ids = self.get_active_drone_ids()
        return drone_code in active_drone_ids
    
    def get_activity_summary(self) -> Dict[str, Any]:
        """
        获取活跃状态统计信息
        :return: 统计信息字典
        """
        try:
            current_time = time.time()
            active_data = self.get_active_drones()
            
            # 按模式代码分组统计
            mode_stats = {}
            for drone_code, status in active_data.items():
                mode_code = status.get('mode_code', 'unknown')
                if mode_code not in mode_stats:
                    mode_stats[mode_code] = 0
                mode_stats[mode_code] += 1
            
            return {
                'total_active': len(active_data),
                'active_drone_ids': list(active_data.keys()),
                'mode_distribution': mode_stats,
                'cache_age': current_time - self._cache_time if self._cache_result else 0,
                'last_check_time': current_time
            }
        except Exception as e:
            logger.error(f"获取活跃状态统计失败: {str(e)}")
            return {
                'total_active': 0,
                'active_drone_ids': [],
                'mode_distribution': {},
                'cache_age': 0,
                'last_check_time': time.time(),
                'error': str(e)
            }
    
    def update_drone_status(self, drone_code: str, status: Dict[str, Any]):
        """更新无人机状态（线程安全）"""
        with self._lock:
            active_drones[drone_code] = status
            # 状态更新时清除缓存
            self._cache_result = {}
            self._cache_time = 0
            self._auto_save_status()
    
    def remove_drone_status(self, drone_code: str):
        """移除无人机状态（线程安全）"""
        with self._lock:
            if drone_code in active_drones:
                del active_drones[drone_code]
                # 状态更新时清除缓存
                self._cache_result = {}
                self._cache_time = 0
                self._auto_save_status()
    
    def cleanup_expired_status(self):
        """清理过期的状态（线程安全）"""
        with self._lock:
            current_time = time.time()
            expired_drones = []
            
            for drone_code, status in active_drones.items():
                last_update = status.get('last_update', 0)
                if current_time - last_update > self.status_timeout:
                    expired_drones.append(drone_code)
            
            # 移除过期的无人机状态
            for drone_code in expired_drones:
                del active_drones[drone_code]
                logger.debug(f"清理过期无人机状态: {drone_code}")
            
            if expired_drones:
                logger.info(f"清理了 {len(expired_drones)} 个过期的无人机状态")
                self._auto_save_status()
    
    def _auto_save_status(self):
        """自动保存状态"""
        if not self.status_file_path:
            return
            
        current_time = time.time()
        if current_time - self.last_save_time < self.save_interval:
            return
            
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.status_file_path), exist_ok=True)
            
            # 只保存最近有更新的无人机状态
            recent_status = {}
            for drone_code, status in active_drones.items():
                if current_time - status.get('last_update', 0) < self.status_timeout:
                    recent_status[drone_code] = status
            
            with open(self.status_file_path, 'w', encoding='utf-8') as f:
                json.dump(recent_status, f, ensure_ascii=False, indent=2)
            
            self.last_save_time = current_time
            logger.debug(f"自动保存无人机状态，共 {len(recent_status)} 个无人机")
            
        except Exception as e:
            logger.error(f"自动保存状态失败: {str(e)}")
    
    def load_persisted_status(self) -> bool:
        """加载持久化的状态"""
        if not self.status_file_path or not os.path.exists(self.status_file_path):
            return False
            
        try:
            with self._lock:
                with open(self.status_file_path, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)
                    active_drones.update(status_data)
                    logger.info(f"成功加载持久化的无人机状态，共 {len(status_data)} 个无人机")
                    return True
                    
        except Exception as e:
            logger.error(f"加载持久化状态失败: {str(e)}")
            return False
    
    def save_status_manually(self) -> bool:
        """手动保存状态"""
        if not self.status_file_path:
            return False
            
        try:
            with self._lock:
                # 确保目录存在
                os.makedirs(os.path.dirname(self.status_file_path), exist_ok=True)
                
                current_time = time.time()
                recent_status = {}
                for drone_code, status in active_drones.items():
                    if current_time - status.get('last_update', 0) < self.status_timeout:
                        recent_status[drone_code] = status
                
                with open(self.status_file_path, 'w', encoding='utf-8') as f:
                    json.dump(recent_status, f, ensure_ascii=False, indent=2)
                
                logger.info(f"手动保存无人机状态，共 {len(recent_status)} 个无人机")
                return True
                
        except Exception as e:
            logger.error(f"手动保存状态失败: {str(e)}")
            return False
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        with self._lock:
            current_time = time.time()
            active_count = 0
            total_count = len(active_drones)
            
            for status in active_drones.values():
                if status.get('is_active', False):
                    active_count += 1
            
            return {
                'total_drones': total_count,
                'active_drones': active_count,
                'last_save_time': self.last_save_time,
                'last_cleanup_time': self.last_cleanup_time
            }

# 全局变量，用于存储无人机状态信息
# 键为无人机ID (device_sn)，值为包含状态信息的字典
# 例如: {
#   'device_sn': 'xxx', 
#   'mode_code': 4, 
#   'is_active': True, 
#   'last_update': timestamp,
#   'longitude': 120.123456,  # 经度
#   'latitude': 30.123456     # 纬度
# }
active_drones: Dict[str, Dict[str, Any]] = {}

# 创建共享状态管理器实例
shared_state_manager = SharedStateManager()

# 如果将来需要更多共享状态，可以在这里添加 