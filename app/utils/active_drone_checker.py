"""
简化的活跃无人机检查器
按照配置列表 → WebSocket数据 → 视频流的顺序进行检查
"""

import logging
import time
from typing import List, Optional, Dict, Any
from app.utils.config import config
from app.utils.shared_state import active_drones

logger = logging.getLogger(__name__)

class ActiveDroneChecker:
    """
    活跃无人机检查器
    按照资源消耗从小到大的顺序进行检查：
    1. 配置列表检查（最轻量）
    2. WebSocket数据检查（中等）
    3. 视频流状态检查（最重）
    """
    
    def __init__(self, video_manager=None):
        """
        初始化检查器
        :param video_manager: 视频流管理器，用于检查视频流状态
        """
        self.video_manager = video_manager
        self.websocket_timeout = 300  # WebSocket数据超时时间（5分钟）
        
    def set_video_manager(self, video_manager):
        """设置视频流管理器"""
        self.video_manager = video_manager
        
    def is_drone_active(self, drone_code: str) -> bool:
        """
        检查指定无人机是否活跃
        :param drone_code: 无人机编号
        :return: 是否活跃
        """
        logger.debug(f"开始检查无人机 {drone_code} 的活跃状态")
        
        # 1. 配置列表检查（最轻量，优先检查）
        if not self._is_in_config_list(drone_code):
            logger.debug(f"无人机 {drone_code} 不在配置列表中，判定为非活跃")
            return False
            
        # 2. WebSocket数据检查（中等资源消耗）
        if not self._has_valid_websocket_data(drone_code):
            logger.debug(f"无人机 {drone_code} 没有有效的WebSocket数据，判定为非活跃")
            return False
            
        # 3. 视频流状态检查（最重资源消耗，最后检查）
        if not self._has_active_video_stream(drone_code):
            logger.debug(f"无人机 {drone_code} 没有活跃的视频流，判定为非活跃")
            return False
            
        logger.info(f"无人机 {drone_code} 通过所有检查，判定为活跃")
        return True
        
    def get_active_drones(self) -> List[str]:
        """
        获取所有活跃的无人机列表
        :return: 活跃无人机编号列表
        """
        logger.debug("开始获取活跃无人机列表")
        
        # 获取配置中的所有无人机
        config_drones = config.get_drone_codes()
        logger.debug(f"配置中的无人机列表: {config_drones}")
        
        active_drones_list = []
        
        for drone_code in config_drones:
            if self.is_drone_active(drone_code):
                active_drones_list.append(drone_code)
                
        logger.info(f"活跃无人机检查完成，共找到 {len(active_drones_list)} 个活跃无人机: {active_drones_list}")
        return active_drones_list
        
    def _is_in_config_list(self, drone_code: str) -> bool:
        """
        检查无人机是否在配置列表中
        :param drone_code: 无人机编号
        :return: 是否在配置列表中
        """
        config_drones = config.get_drone_codes()
        is_in_config = drone_code in config_drones
        logger.debug(f"配置检查: 无人机 {drone_code} {'在' if is_in_config else '不在'} 配置列表 {config_drones} 中")
        return is_in_config
        
    def _has_valid_websocket_data(self, drone_code: str) -> bool:
        """
        检查无人机是否有有效的WebSocket数据
        :param drone_code: 无人机编号
        :return: 是否有有效数据
        """
        if drone_code not in active_drones:
            logger.debug(f"WebSocket检查: 无人机 {drone_code} 不在 active_drones 中")
            return False
            
        drone_status = active_drones[drone_code]
        
        # 检查是否标记为活跃
        is_active = drone_status.get('is_active', False)
        if not is_active:
            logger.debug(f"WebSocket检查: 无人机 {drone_code} 未标记为活跃状态")
            return False
            
        # 检查数据时效性
        last_update = drone_status.get('last_update', 0)
        current_time = time.time()
        time_diff = current_time - last_update
        
        if time_diff > self.websocket_timeout:
            logger.debug(f"WebSocket检查: 无人机 {drone_code} 数据已过期 ({time_diff:.1f}秒前)")
            return False
            
        # 检查模式代码
        mode_code = drone_status.get('mode_code')
        if mode_code is None:
            logger.debug(f"WebSocket检查: 无人机 {drone_code} 没有模式代码")
            return False
            
        if mode_code not in config.WEBSOCKET_ACTIVE_MODE_CODES:
            logger.debug(f"WebSocket检查: 无人机 {drone_code} 模式代码 {mode_code} 不在活跃列表 {config.WEBSOCKET_ACTIVE_MODE_CODES} 中")
            return False
            
        logger.debug(f"WebSocket检查: 无人机 {drone_code} 有有效的WebSocket数据 (模式: {mode_code}, 更新时间: {time_diff:.1f}秒前)")
        return True
        
    def _has_active_video_stream(self, drone_code: str) -> bool:
        """
        检查无人机是否有活跃的视频流
        :param drone_code: 无人机编号
        :return: 是否有活跃视频流
        """
        if self.video_manager is None:
            logger.warning(f"视频流检查: 视频管理器未设置，跳过无人机 {drone_code} 的视频流检查")
            return True  # 如果没有视频管理器，则跳过视频流检查
            
        try:
            # 检查是否有视频流
            stream_key = f"{drone_code}_drone"
            if not hasattr(self.video_manager, 'streams'):
                logger.debug(f"视频流检查: 视频管理器没有 streams 属性")
                return False
                
            if stream_key not in self.video_manager.streams:
                logger.debug(f"视频流检查: 无人机 {drone_code} 没有视频流 (键: {stream_key})")
                return False
                
            stream = self.video_manager.streams[stream_key]
            
            # 检查流是否活跃
            if hasattr(stream, 'is_active'):
                is_active = stream.is_active()
                logger.debug(f"视频流检查: 无人机 {drone_code} 视频流活跃状态: {is_active}")
                return is_active
            elif hasattr(stream, 'check_stream'):
                # 如果有 check_stream 方法，使用它来检查
                logger.debug(f"视频流检查: 使用 check_stream 方法检查无人机 {drone_code}")
                # 这里需要异步调用，暂时返回True，后续可以改进
                return True
            else:
                logger.debug(f"视频流检查: 无人机 {drone_code} 的视频流对象没有状态检查方法，假设为活跃")
                return True
                
        except Exception as e:
            logger.error(f"视频流检查: 检查无人机 {drone_code} 视频流状态时出错: {str(e)}")
            return False
            
    def get_status_summary(self) -> Dict[str, Any]:
        """
        获取活跃状态摘要
        :return: 状态摘要
        """
        active_drones_list = self.get_active_drones()
        config_drones = config.get_drone_codes()
        
        return {
            'total_config_drones': len(config_drones),
            'active_drones_count': len(active_drones_list),
            'active_drones': active_drones_list,
            'config_drones': config_drones,
            'check_time': time.time()
        }

# 创建全局实例
active_drone_checker = ActiveDroneChecker()
