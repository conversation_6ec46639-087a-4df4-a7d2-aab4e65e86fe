from fastapi import APIRouter, File, UploadFile, Form, WebSocket, HTTPException, Header, Depends, Request
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.websockets import WebSocketState
from typing import Dict, Optional
import cv2
import numpy as np
import os
import uuid
import time
from app.utils.config import config
from app.processor.image_processor import ImagePreprocessor
from app.processor.video_processor import VideoProcessor
from app.processor.stream_processor import StreamProcessor
from app.utils.websocket_manager import manager
import logging
from app.utils.webrtc_utils import WebRTCProcessor
from app.models.yolo_model import YOLOModel

logger = logging.getLogger('app.api.detect')
router = APIRouter()

# 存储所有活跃的流处理器
stream_processors: Dict[str, StreamProcessor] = {}

# 存储所有活跃的WebRTC处理器
webrtc_processors: Dict[str, WebRTCProcessor] = {}

# 请求频率限制
class RateLimiter:
    def __init__(self, max_requests: int = 5, time_window: int = 60):
        """
        初始化请求频率限制器
        :param max_requests: 时间窗口内允许的最大请求数
        :param time_window: 时间窗口大小（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = {}  # 存储每个IP的请求记录 {ip: [timestamp1, timestamp2, ...]}
        
    def is_allowed(self, ip: str) -> bool:
        """
        检查请求是否被允许
        :param ip: 客户端IP地址
        :return: 如果允许请求，返回True，否则返回False
        """
        current_time = time.time()
        
        # 如果IP不在记录中，添加它
        if ip not in self.requests:
            self.requests[ip] = []
            
        # 清理过期的请求记录
        self.requests[ip] = [t for t in self.requests[ip] if current_time - t < self.time_window]
        
        # 检查请求数是否超过限制
        if len(self.requests[ip]) >= self.max_requests:
            return False
            
        # 添加新的请求记录
        self.requests[ip].append(current_time)
        return True

# 创建请求频率限制器实例
rate_limiter = RateLimiter(
    max_requests=config.API_RATE_LIMIT_MAX_REQUESTS,
    time_window=config.API_RATE_LIMIT_TIME_WINDOW
)

# 请求频率限制依赖项
async def check_rate_limit(request: Request):
    """
    检查请求频率是否超过限制
    :param request: FastAPI请求对象
    :return: 如果未超过限制，返回True，否则抛出异常
    """
    client_ip = request.client.host
    
    if not rate_limiter.is_allowed(client_ip):
        logger.warning(f"请求频率超过限制: {client_ip}")
        raise HTTPException(
            status_code=429,
            detail="请求频率过高，请稍后再试"
        )
    return True

@router.get("/", response_class=HTMLResponse)
async def get_image_page():
    """渲染图像检测页面"""
    with open("app/templates/index.html", "r", encoding="utf-8") as f:
        content = f.read()
    return content

@router.get("/video", response_class=HTMLResponse)
async def get_video_page():
    """渲染视频检测页面"""
    with open("app/templates/video_index.html", "r", encoding="utf-8") as f:
        content = f.read()
    return content

@router.get("/stream", response_class=HTMLResponse)
async def get_stream_page():
    """渲染RTMP流检测页面"""
    with open("app/templates/stream_index.html", "r", encoding="utf-8") as f:
        content = f.read()
    return content

@router.get("/webrtc", response_class=HTMLResponse)
async def get_webrtc_page():
    """渲染WebRTC测试页面"""
    with open("app/templates/webrtc_test.html", "r", encoding="utf-8") as f:
        content = f.read()
    return content

@router.post("/image")
async def detect_image(
    file: UploadFile = File(...),
    prompt: str = Form(...),
    model_type: str = Form(...),  # 'cv' 或 'multimodal'
    confidence_threshold: float = Form(0.5)  # 置信度阈值，默认为0.5
):
    """图像检测接口"""
    try:
        # 读取图像数据并转换为numpy数组
        image_bytes = await file.read()
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # 根据model_type初始化处理器
        processor = ImagePreprocessor(model_type=model_type)
        results = processor.detect(image, prompt, confidence_threshold)
        
        # 如果结果中包含error字段，返回500错误
        if "error" in results:
            logger.error(f"图像处理失败: {results['error']}")
            return JSONResponse(content=results, status_code=500)
            
        return JSONResponse(content=results)
    except Exception as e:
        logger.error(f"请求处理失败: {str(e)}", exc_info=True)
        return JSONResponse(
            content={
                "target": prompt,
                "detections": [],
                "count": 0,
                "annotated_image": None,
                "error": str(e)
            },
            status_code=500
        )

@router.post("/video")
async def detect_video(
    file: UploadFile = File(...),
    prompt: str = Form(...),
    model_type: str = Form(...),
    max_duration: int = Form(None),
    confidence_threshold: float = Form(0.5)  # 置信度阈值，默认为0.5
):
    """视频检测接口"""
    global logger
    logger.info(f"收到视频处理请求: 文件={file.filename}, 目标={prompt}, "
                f"最大时长={max_duration}, 模型类型={model_type}, 置信度阈值={confidence_threshold}")
    
    # 保存上传的视频文件
    temp_dir = config.TEMP_VIDEO_DIR
    os.makedirs(temp_dir, exist_ok=True)
    video_id = str(uuid.uuid4())
    video_path = os.path.join(temp_dir, f"{video_id}_{file.filename}")

    try:
        with open(video_path, "wb") as f:
            f.write(await file.read())

        # 根据model_type初始化处理器
        processor = VideoProcessor(model_type=model_type)
        result = processor.process_video(video_path, prompt, max_duration, confidence_threshold)
        
        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"视频处理失败: {str(e)}")
        return JSONResponse(content={"error": str(e)}, status_code=500)
    finally:
        if os.path.exists(video_path):
            os.remove(video_path)
            logger.debug(f"临时文件已删除: {video_path}")

# 存储活动的流处理器
active_processors: Dict[str, StreamProcessor] = {}

@router.websocket("/ws/stream/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接端点"""
    try:
        await manager.connect(client_id, websocket)
        logger.info(f"WebSocket连接成功建立: {client_id}")
        
        while True:
            try:
                data = await websocket.receive_json()
                if data.get("type") == "stop":
                    logger.info(f"收到停止命令: {client_id}")
                    await manager.disconnect(client_id)
                    break
            except Exception as e:
                logger.error(f"WebSocket数据接收错误: {str(e)}")
                break
            
    except Exception as e:
        logger.error(f"WebSocket连接错误: {client_id}, 错误: {str(e)}")
    finally:
        logger.info(f"WebSocket连接关闭: {client_id}")
        await manager.disconnect(client_id)


@router.post("/stream")
async def detect_stream(
    stream_url: str = Form(...),
    prompt: str = Form(...),
    model_type: str = Form(...)
):
    """RTMP流检测接口"""
    try:
        logger.info(f"接收到流处理请求: URL={stream_url}, prompt={prompt}, model={model_type}")

        # 验证RTMP URL
        if not stream_url.startswith('rtmp://'):
            return JSONResponse(
                content={"error": "Invalid RTMP URL"},
                status_code=400
            )
        
        client_id = str(uuid.uuid4())
        processor = StreamProcessor(model_type=model_type)
        manager.add_processor(client_id, processor)
        
        # 使用WebSocket发送结果
        async def send_results(results):    
            # 实现WebSocket发送逻辑
            await manager.send_results(client_id, results)
            
        success = processor.start_processing(stream_url, prompt, send_results)
        if not success:
            return JSONResponse(
                content={"error": "无法启动流处理"},
                status_code=500
            )
            
        return JSONResponse(content={"status": "started", "client_id": client_id})
        
    except Exception as e:
        logger.error(f"流处理失败: {str(e)}")
        return JSONResponse(
            content={"error": str(e)},
            status_code=500
        )
    
@router.post("/stream/stop")
async def stop_stream(client_id: str):
    """停止流处理"""
    manager.disconnect(client_id)
    return JSONResponse(content={"status": "stopped"})

# WebRTC相关路由
@router.post("/webrtc/offer")
async def webrtc_offer(offer: dict):
    """处理WebRTC offer"""
    try:
        client_id = offer.get("client_id")
        if not client_id:
            raise HTTPException(status_code=400, detail="Missing client_id")
            
        # 如果已存在相同client_id的处理器，先停止它
        if client_id in webrtc_processors:
            await webrtc_processors[client_id].stop()
            
        # 创建新的WebRTC处理器
        processor = WebRTCProcessor()
        webrtc_processors[client_id] = processor
        
        # 启动WebRTC处理
        answer = await processor.start(offer["sdp"])
        
        if not answer:
            raise HTTPException(status_code=500, detail="Failed to create WebRTC answer")
            
        logger.info(f"WebRTC流启动成功: {client_id}")
        return {
            "sdp": answer.sdp,
            "type": answer.type
        }
        
    except Exception as e:
        logger.error(f"WebRTC处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/webrtc/stop/{client_id}")
async def stop_webrtc(client_id: str):
    """停止WebRTC流处理"""
    try:
        if client_id in webrtc_processors:
            processor = webrtc_processors[client_id]
            await processor.stop()
            del webrtc_processors[client_id]
            logger.info(f"WebRTC流停止成功: {client_id}")
            return {"status": "success"}
        else:
            raise HTTPException(status_code=404, detail="Client not found")
            
    except Exception as e:
        logger.error(f"停止WebRTC流失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/webrtc/status/{client_id}")
async def get_webrtc_status(client_id: str):
    """
    获取WebRTC流状态
    :param client_id: 客户端ID
    :return: 流状态信息
    """
    try:
        if client_id in stream_processors:
            processor = stream_processors[client_id]
            return {
                "status": "running" if processor.is_running else "stopped",
                "client_id": client_id
            }
        else:
            return {
                "status": "not_found",
                "client_id": client_id
            }
            
    except Exception as e:
        logger.error(f"获取WebRTC状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# API密钥验证依赖项
async def verify_api_key(x_api_key: str = Header(...)):
    """
    验证API密钥
    :param x_api_key: 请求头中的API密钥
    :return: 如果验证通过，返回True，否则抛出异常
    """
    # 从配置中获取有效的API密钥
    valid_api_key = config.API_KEY
    if x_api_key != valid_api_key:
        logger.warning(f"API密钥验证失败: {x_api_key}")
        raise HTTPException(
            status_code=401,
            detail="无效的API密钥"
        )
    return True

# IP地址限制依赖项
async def verify_client_ip(request: Request):
    """
    验证客户端IP地址
    :param request: FastAPI请求对象
    :return: 如果验证通过，返回True，否则抛出异常
    """
    client_ip = request.client.host
    allowed_ips = config.ALLOWED_IPS
    
    # 如果允许的IP列表为空，则不进行IP限制
    if not allowed_ips:
        return True
        
    if client_ip not in allowed_ips:
        logger.warning(f"IP地址验证失败: {client_ip}")
        raise HTTPException(
            status_code=403,
            detail="您的IP地址没有权限访问此API"
        )
    return True

@router.post("/reload_model")
async def reload_model(
    request: Request,
    model_path: str = Form(...),
    api_key_verified: bool = Depends(verify_api_key),
    ip_verified: bool = Depends(verify_client_ip),
    rate_limit_checked: bool = Depends(check_rate_limit)
):
    """
    热更新模型接口
    :param model_path: 新模型的路径
    :param api_key_verified: API密钥验证结果
    :param ip_verified: IP地址验证结果
    :param rate_limit_checked: 请求频率限制验证结果
    :return: 更新结果
    """
    try:
        logger.info(f"接收到模型热更新请求: 模型路径={model_path}")
        
        # 验证模型文件是否存在
        if not os.path.exists(model_path):
            logger.error(f"模型文件不存在: {model_path}")
            return JSONResponse(
                content={"error": f"模型文件不存在: {model_path}"},
                status_code=400
            )
            
        # 创建一个临时YOLOModel实例来测试模型是否可用
        try:
            test_model = YOLOModel(model_path)
            logger.info(f"模型文件测试加载成功: {model_path}")
        except Exception as e:
            logger.error(f"模型文件测试加载失败: {str(e)}")
            return JSONResponse(
                content={"error": f"模型文件无效: {str(e)}"},
                status_code=400
            )
            
        # 更新配置文件中的模型路径
        # 注意：这里不直接修改配置文件，而是更新运行时配置
        config.config_data['models']['yolo']['model_path'] = model_path
        logger.info(f"已更新运行时配置中的模型路径: {model_path}")
        
        # 获取所有活跃的图像处理器并更新模型
        updated_count = 0
        failed_count = 0
        
        # 更新所有活跃的图像处理器中的模型
        from app.processor.stream_processor import StreamProcessor
        from app.services.monitor_service import MonitorService
        
        # 获取MonitorService实例
        monitor_service = MonitorService()
        
        # 更新StreamMonitor中的所有处理器
        if hasattr(monitor_service, 'stream_monitor') and monitor_service.stream_monitor:
            for processor in monitor_service.stream_monitor.processors.values():
                if hasattr(processor, 'image_processor') and processor.image_processor:
                    if hasattr(processor.image_processor, 'model') and processor.image_processor.model:
                        try:
                            # 调用模型的reload_model方法
                            success = processor.image_processor.model.reload_model(model_path)
                            if success:
                                updated_count += 1
                                logger.info(f"成功更新处理器模型: {processor.stream_key}")
                            else:
                                failed_count += 1
                                logger.warning(f"更新处理器模型失败: {processor.stream_key}")
                        except Exception as e:
                            failed_count += 1
                            logger.error(f"更新处理器模型异常: {processor.stream_key}, 错误: {str(e)}")
        
        return JSONResponse(content={
            "status": "success",
            "message": f"模型热更新完成，成功更新 {updated_count} 个处理器，失败 {failed_count} 个",
            "updated_count": updated_count,
            "failed_count": failed_count,
            "new_model_path": model_path
        })
        
    except Exception as e:
        logger.error(f"模型热更新失败: {str(e)}")
        return JSONResponse(
            content={"error": str(e)},
            status_code=500
        )

