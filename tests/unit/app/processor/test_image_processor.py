"""
ImagePreprocessor类的单元测试
"""
import pytest
import numpy as np
import cv2
from unittest.mock import MagicMock, patch, AsyncMock
import os
import sys
from enum import Enum
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 导入被测试的类
from app.processor.image_processor import ImagePreprocessor, PreprocessMethod


@pytest.fixture
def image_processor(mock_config, mock_file_manager):
    """创建ImagePreprocessor实例的fixture"""
    logger.info("设置 image_processor fixture")
    # 模拟RealESRGANer类
    with patch('app.processor.image_processor.RealESRGANer'), \
         patch('app.processor.image_processor.RRDBNet'), \
         patch('app.processor.image_processor.file_manager', mock_file_manager), \
         patch('app.processor.image_processor.config', mock_config):
        # 创建一个简单的模型模拟对象
        model_mock = MagicMock()
        model_mock.detect.return_value = {
            "detections": [
                {
                    "bbox": [10, 10, 50, 50],
                    "confidence": 0.95,
                    "class": "ship"
                }
            ],
            "target_category": "船只",
            "error_code": "0"
        }
        
        processor = ImagePreprocessor(target_size=(640, 640))
        processor.model = model_mock
        processor.preprocess = True  # 确保这是一个布尔值而不是MagicMock
        
        # 设置upsampler属性，这是_super_resolution方法实际使用的
        processor.upsampler = MagicMock()
        processor.upsampler.enhance.return_value = (np.zeros((200, 200, 3), dtype=np.uint8), None)
        
        logger.info("image_processor fixture 设置完成")
        yield processor
        logger.info("清理 image_processor fixture")


def test_init(mock_config):
    """测试初始化"""
    logger.info("开始测试 ImagePreprocessor 初始化")
    with patch('app.processor.image_processor.RealESRGANer'), \
         patch('app.processor.image_processor.RRDBNet'), \
         patch('app.processor.image_processor.config', mock_config):
        # 确保mock_config.PREPROCESS是一个布尔值
        mock_config.PREPROCESS = True
        
        processor = ImagePreprocessor()
        assert processor.target_size == (640, 640)
        # 检查preprocess属性是否为True
        assert processor.preprocess is True
    logger.info("ImagePreprocessor 初始化测试完成")


def test_resize(image_processor, sample_image):
    """测试调整图像大小功能"""
    logger.info("开始测试 resize 方法")
    # 创建一个100x100的测试图像
    result = image_processor._resize(sample_image)
    
    # 验证结果
    assert result.shape[:2] == image_processor.target_size
    logger.info("resize 方法测试完成")


def test_histogram_equalization(image_processor, sample_image):
    """测试直方图均衡化功能"""
    logger.info("开始测试 histogram_equalization 方法")
    # 创建一个有对比度的图像
    test_image = np.zeros((100, 100), dtype=np.uint8)
    test_image[25:75, 25:75] = 200  # 中间区域设为白色
    
    # 转换为3通道图像
    test_image = cv2.cvtColor(test_image, cv2.COLOR_GRAY2BGR)
    
    result = image_processor._histogram_equalization(test_image)
    
    # 验证结果
    assert result.shape == test_image.shape
    # 直方图均衡化后，图像应该有更多的对比度
    assert np.std(result) != np.std(test_image)
    logger.info("histogram_equalization 方法测试完成")


def test_sharpen(image_processor, sample_image):
    """测试图像锐化功能"""
    logger.info("开始测试 sharpen 方法")
    result = image_processor._sharpen(sample_image)
    
    # 验证结果
    assert result.shape == sample_image.shape
    logger.info("sharpen 方法测试完成")


def test_denoise(image_processor, sample_image):
    """测试图像去噪功能"""
    logger.info("开始测试 denoise 方法")
    # 添加一些噪声
    noisy_image = sample_image.copy()
    noise = np.random.randint(0, 50, size=sample_image.shape, dtype=np.uint8)
    noisy_image = cv2.add(sample_image, noise)
    
    result = image_processor._denoise(noisy_image)
    
    # 验证结果
    assert result.shape == noisy_image.shape
    logger.info("denoise 方法测试完成")


def test_normalize(image_processor, sample_image):
    """测试图像归一化功能"""
    logger.info("开始测试 normalize 方法")
    result = image_processor._normalize(sample_image)
    
    # 验证结果
    assert result.shape == sample_image.shape
    # 归一化后的值应该在0-1之间
    assert np.max(result) <= 1.0
    assert np.min(result) >= 0.0
    logger.info("normalize 方法测试完成")


def test_super_resolution(image_processor, sample_image):
    """测试超分辨率功能"""
    logger.info("开始测试 super_resolution 方法")
    
    # 创建一个100x100的测试图像
    test_image = np.zeros((100, 100, 3), dtype=np.uint8)
    
    # 临时禁用target_size调整，确保返回的就是enhance方法的输出
    original_target_size = image_processor.target_size
    image_processor.target_size = None
    
    try:
        # 调用被测试方法
        result = image_processor._super_resolution(test_image)
        
        # 验证结果
        logger.info(f"输入图像形状: {test_image.shape}")
        logger.info(f"输出图像形状: {result.shape}")
        assert result.shape == (200, 200, 3)
        image_processor.upsampler.enhance.assert_called_once()
    finally:
        # 恢复原始target_size
        image_processor.target_size = original_target_size
    
    logger.info("super_resolution 方法测试完成")


def test_preprocess_image(image_processor, sample_image):
    """测试图像预处理功能"""
    logger.info("开始测试 preprocess_image 方法")
    # 模拟所有预处理方法
    with patch.object(image_processor, '_resize', return_value=sample_image), \
         patch.object(image_processor, '_histogram_equalization', return_value=sample_image), \
         patch.object(image_processor, '_sharpen', return_value=sample_image), \
         patch.object(image_processor, '_denoise', return_value=sample_image), \
         patch.object(image_processor, '_normalize', return_value=sample_image), \
         patch.object(image_processor, '_super_resolution', return_value=sample_image):
        
        # 设置预处理方法
        image_processor.preprocess_methods = [
            PreprocessMethod.RESIZE,
            PreprocessMethod.HISTOGRAM_EQUALIZATION,
            PreprocessMethod.SHARPEN,
            PreprocessMethod.DENOISE,
            PreprocessMethod.NORMALIZE
        ]
        
        result = image_processor._preprocess_image(sample_image)
        
        # 验证结果
        assert result.shape == sample_image.shape
        # 验证每个预处理方法都被调用
        image_processor._resize.assert_called_once()
        image_processor._histogram_equalization.assert_called_once()
        image_processor._sharpen.assert_called_once()
        image_processor._denoise.assert_called_once()
        image_processor._normalize.assert_called_once()
        # 超分辨率没有在预处理方法列表中，不应该被调用
        image_processor._super_resolution.assert_not_called()
    
    logger.info("preprocess_image 方法测试完成")


def test_detect(image_processor, sample_image):
    """测试目标检测功能"""
    logger.info("开始测试 detect 方法")
    # 模拟预处理
    with patch.object(image_processor, '_preprocess_image', return_value=sample_image):
        result = image_processor.detect(sample_image, "船只")
        
        # 验证结果
        assert "detections" in result
        assert len(result["detections"]) == 1
        assert result["detections"][0]["class"] == "ship"
        assert result["target_category"] == "船只"
        # 验证预处理被调用
        image_processor._preprocess_image.assert_called_once_with(sample_image)
        # 验证模型检测被调用
        image_processor.model.detect.assert_called_once_with(sample_image, "船只")
    
    logger.info("detect 方法测试完成")


@pytest.mark.asyncio
async def test_save_detection_images(image_processor, sample_image):
    """测试保存检测图像功能"""
    logger.info("开始测试 save_detection_images 方法")
    # 模拟检测结果
    detection_result = {
        "detections": [
            {
                "bbox": [10, 10, 50, 50],
                "confidence": 0.95,
                "class": "ship"
            }
        ],
        "target_category": "船只"
    }
    
    # 调用被测试方法
    timestamp = 1609459200000  # 2021-01-01 00:00:00
    init_path, res_path = await image_processor.save_detection_images(sample_image, detection_result, timestamp)
    
    # 验证结果
    assert init_path == "/path/to/test_image.jpg"
    assert res_path == "/path/to/test_image.jpg"
    # 验证文件保存被调用了两次（原始图像和标注后的图像）
    assert image_processor._save_image.call_count == 0  # 因为我们模拟了file_manager.save_file
    
    logger.info("save_detection_images 方法测试完成") 