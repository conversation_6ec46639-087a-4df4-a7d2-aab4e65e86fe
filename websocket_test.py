#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import json
import logging
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("websocket_test")

# WebSocket配置
WEBSOCKET_URL = 'http://xxx.xxx.xxx.xxx:6789/'
WEBSOCKET_USERNAME = 'xxxx'
WEBSOCKET_PASSWORD = 'xxxx'
WEBSOCKET_FLAG = 1

class WebSocketTest:
    """WebSocket测试客户端"""
    
    def __init__(self):
        """初始化WebSocket测试客户端"""
        self.token = None
        self.workspace_id = None
        self.ws = None
        self.session = None
        self._running = False
        
    async def get_token(self) -> bool:
        """
        获取认证token
        :return: 是否成功获取token
        """
        try:
            base_url = WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/manage/api/v1/login",
                    json={
                        "username": WEBSOCKET_USERNAME,
                        "password": WEBSOCKET_PASSWORD,
                        "flag": WEBSOCKET_FLAG
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'data' in data:
                            self.token = data['data']['access_token']
                            self.workspace_id = data['data']['workspace_id']
                            logger.info("成功获取新token")
                            logger.info(f"Token: {self.token}")
                            logger.info(f"Workspace ID: {self.workspace_id}")
                            return True
                    logger.error(f"获取token失败: {response.status} - {await response.text()}")
                    return False
        except Exception as e:
            logger.error(f"获取token异常: {str(e)}")
            return False

    def get_websocket_url(self) -> str:
        """
        获取WebSocket URL
        :return: 完整的WebSocket URL
        """
        if not self.token:
            raise ValueError("Token未初始化")
            
        # 从 base_url 中提取主机和端口
        base_url = WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
        # 替换 http:// 为 ws://
        ws_url = base_url.replace('http://', 'ws://')
        # 添加 WebSocket 路径和 token
        return f"{ws_url}/api/v1/ws?x-auth-token={self.token}"

    async def connect(self) -> bool:
        """
        建立WebSocket连接
        :return: 是否成功建立连接
        """
        try:
            # 如果没有token，获取token
            if not self.token:
                if not await self.get_token():
                    return False

            # 获取WebSocket URL
            ws_url = self.get_websocket_url()
            logger.info(f"WebSocket URL: {ws_url}")
            
            # 创建新的会话
            if self.session:
                await self.session.close()
            self.session = aiohttp.ClientSession()
            
            # 建立WebSocket连接，使用token进行认证
            self.ws = await self.session.ws_connect(
                ws_url,
                headers={
                    "Authorization": f"Bearer {self.token}",
                    "workspace-id": self.workspace_id
                }
            )
            
            logger.info("WebSocket连接成功建立")
            return True
            
        except aiohttp.ClientError as e:
            logger.error(f"WebSocket连接失败: {str(e)}")
            if "401" in str(e):
                logger.info("尝试重新获取token并重连")
                self.token = None
                return await self.connect()
            return False
        except Exception as e:
            logger.error(f"建立WebSocket连接时发生异常: {str(e)}")
            return False

    async def _send_pong(self) -> None:
        """发送pong消息"""
        if self.ws and not self.ws.closed:
            try:
                await self.ws.send_json({"type": "pong", "timestamp": int(datetime.now().timestamp())})
                logger.info("发送pong消息")
            except Exception as e:
                logger.error(f"发送pong消息失败: {str(e)}")

    async def _handle_message(self, message: str) -> None:
        """
        处理接收到的WebSocket消息
        :param message: 消息内容
        """
        try:
            data = json.loads(message)
            
            # 处理ping消息
            if data.get("type") == "ping":
                await self._send_pong()
                logger.info("收到ping消息，已回复pong")
            else:
                # 打印接收到的消息
                logger.info(f"收到消息: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    
        except json.JSONDecodeError:
            logger.error(f"解析消息失败: {message}")
        except Exception as e:
            logger.error(f"处理消息时发生异常: {str(e)}")

    async def start(self) -> None:
        """启动WebSocket客户端"""
        if self._running:
            return
            
        self._running = True
        
        while self._running:
            try:
                if not self.ws and not await self.connect():
                    logger.error("连接失败，5秒后重试")
                    await asyncio.sleep(5)
                    continue
                
                logger.info("开始接收消息...")
                async for msg in self.ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        await self._handle_message(msg.data)
                    elif msg.type == aiohttp.WSMsgType.CLOSED:
                        logger.warning("WebSocket连接已关闭")
                        break
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        logger.error(f"WebSocket错误: {msg.data}")
                        break
                
                if self._running:
                    logger.warning("WebSocket连接断开，尝试重连")
                    await asyncio.sleep(5)
                    
            except Exception as e:
                logger.error(f"WebSocket处理异常: {str(e)}")
                await asyncio.sleep(5)

    async def stop(self) -> None:
        """停止WebSocket客户端"""
        self._running = False
        if self.ws:
            await self.ws.close()
        if self.session:
            await self.session.close()
        logger.info("WebSocket客户端已停止")

async def main():
    """主函数"""
    client = WebSocketTest()
    try:
        # 启动客户端
        await client.start()
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止...")
    finally:
        # 确保资源被正确释放
        await client.stop()

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main()) 