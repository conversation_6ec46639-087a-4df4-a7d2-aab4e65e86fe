import asyncio
import cv2
import logging
import threading
import time
from typing import Optional, Callable
import numpy as np
from app.utils.config import config
from app.utils.frame_data import FrameData
from app.utils.async_frame_queue import AsyncFrameQueue
from .rtmp_connection_manager import RTMPConnectionManager

logger = logging.getLogger(__name__)

class FrameProducer:
    """帧生产者，独立线程中读取视频帧并放入队列"""
    
    def __init__(self, 
                 connection_manager: RTMPConnectionManager, 
                 frame_queue: AsyncFrameQueue,
                 stream_key: str,
                 on_error: Optional[Callable[[Exception], None]] = None):
        """
        初始化帧生产者
        :param connection_manager: 连接管理器
        :param frame_queue: 帧队列
        :param stream_key: 流标识符
        :param on_error: 错误回调函数
        """
        self.connection_manager = connection_manager
        self.frame_queue = frame_queue
        self.stream_key = stream_key
        self.on_error = on_error
        
        # 控制变量
        self._running = False
        self._producer_thread: Optional[threading.Thread] = None
        self._loop = asyncio.get_event_loop()
        
        # 统计变量
        self._frame_count = 0
        self._start_time = 0.0
        self._last_stats_time = 0.0
        self._last_frame_time = 0.0
        
        # 配置参数
        self._read_timeout = config.PRODUCER_READ_TIMEOUT
        self._max_consecutive_failures = config.PRODUCER_MAX_CONSECUTIVE_FAILURES
        self._failure_sleep_time = config.PRODUCER_FAILURE_SLEEP_TIME
        self._stats_interval = config.PRODUCER_STATS_INTERVAL
        
        # 错误统计
        self._consecutive_failures = 0
        self._total_failures = 0
        self._successful_reads = 0
        
    def start_producing(self) -> bool:
        """
        启动帧生产
        :return: 是否成功启动
        """
        if self._running:
            logger.warning(f"帧生产者已在运行: {self.stream_key}")
            return False
        
        logger.info(f"启动帧生产者: {self.stream_key}")
        
        self._running = True
        self._start_time = time.time()
        self._last_stats_time = self._start_time
        self._frame_count = 0
        self._consecutive_failures = 0
        
        # 创建生产者线程
        self._producer_thread = threading.Thread(
            target=self._produce_frames,
            name=f"FrameProducer-{self.stream_key}",
            daemon=True
        )
        self._producer_thread.start()
        
        return True
    
    def stop_producing(self) -> None:
        """停止帧生产"""
        if not self._running:
            return
        
        logger.info(f"停止帧生产者: {self.stream_key}")
        self._running = False
        
        # 等待生产者线程结束
        if self._producer_thread:
            self._producer_thread.join(timeout=5.0)
            if self._producer_thread.is_alive():
                logger.warning(f"生产者线程未能正常结束: {self.stream_key}")
        
        self._log_final_stats()
    
    def _produce_frames(self) -> None:
        """生产者主循环"""
        logger.info(f"开始帧生产循环: {self.stream_key}")
        
        while self._running:
            try:
                # 检查连接状态
                if not self.connection_manager.is_connected():
                    logger.warning(f"连接未建立，等待重连: {self.stream_key}")
                    self._handle_read_failure()
                    continue
                
                # 获取VideoCapture对象
                cap = self.connection_manager.get_video_capture()
                if not cap:
                    logger.warning(f"无法获取VideoCapture对象: {self.stream_key}")
                    self._handle_read_failure()
                    continue
                
                # 读取帧
                start_time = time.time()
                ret, frame = cap.read()
                read_time = time.time() - start_time
                
                # 检查读取结果
                if not ret or frame is None or frame.size == 0:
                    self._handle_read_failure()
                    continue
                
                # 成功读取帧
                self._handle_successful_read(frame, read_time)
                
            except Exception as e:
                logger.error(f"生产者循环出错: {self.stream_key}, 错误: {str(e)}")
                self._handle_exception(e)
        
        logger.info(f"帧生产循环结束: {self.stream_key}")
    
    def _handle_successful_read(self, frame: np.ndarray, read_time: float) -> None:
        """处理成功读取的帧"""
        current_time = time.time()
        self._frame_count += 1
        self._successful_reads += 1
        self._consecutive_failures = 0
        self._last_frame_time = current_time
        
        # 创建帧数据
        frame_data = FrameData(
            frame=frame.copy(),
            timestamp=current_time * 1000,  # 转换为毫秒
            frame_id=self._frame_count,
            metadata={
                "stream_key": self.stream_key,
                "read_time": read_time,
                "producer_time": current_time
            }
        )
        
        # 异步放入队列
        try:
            future = asyncio.run_coroutine_threadsafe(
                self.frame_queue.put_frame(frame_data),
                self._loop
            )
            success = future.result(timeout=0.1)  # 短超时，避免阻塞
            
            if not success:
                logger.warning(f"帧入队失败: {self.stream_key}, 帧ID: {self._frame_count}")
                
        except Exception as e:
            logger.error(f"帧入队出错: {self.stream_key}, 错误: {str(e)}")
        
        # 定期输出统计信息
        if current_time - self._last_stats_time >= self._stats_interval:
            self._log_stats()
            self._last_stats_time = current_time
        
        # 如果读取时间过长，记录警告
        if read_time > 1.0:
            logger.warning(f"帧读取耗时过长: {self.stream_key}, 耗时: {read_time:.2f}秒")
    
    def _handle_read_failure(self) -> None:
        """处理读取失败"""
        self._consecutive_failures += 1
        self._total_failures += 1
        
        if self._consecutive_failures >= self._max_consecutive_failures:
            logger.error(f"连续读取失败次数达到{self._max_consecutive_failures}，停止生产: {self.stream_key}")
            self._running = False
            return
        
        # 短暂等待后继续尝试
        time.sleep(self._failure_sleep_time)
    
    def _handle_exception(self, exception: Exception) -> None:
        """处理异常"""
        self._consecutive_failures += 1
        self._total_failures += 1
        
        # 调用错误回调
        if self.on_error:
            try:
                self.on_error(exception)
            except Exception as e:
                logger.error(f"错误回调函数出错: {str(e)}")
        
        # 如果连续异常次数过多，停止生产
        if self._consecutive_failures >= self._max_consecutive_failures:
            logger.error(f"连续异常次数达到{self._max_consecutive_failures}，停止生产: {self.stream_key}")
            self._running = False
            return
        
        # 等待后继续
        time.sleep(self._failure_sleep_time)
    
    def _log_stats(self) -> None:
        """记录统计信息"""
        current_time = time.time()
        elapsed_time = current_time - self._start_time
        
        if elapsed_time > 0:
            fps = self._frame_count / elapsed_time
            success_rate = (self._successful_reads / (self._successful_reads + self._total_failures)) * 100 if (self._successful_reads + self._total_failures) > 0 else 0
            
            logger.info(f"生产者统计 [{self.stream_key}] - "
                       f"总帧数: {self._frame_count}, "
                       f"FPS: {fps:.2f}, "
                       f"成功率: {success_rate:.1f}%, "
                       f"队列大小: {self.frame_queue.get_queue_size()}")
    
    def _log_final_stats(self) -> None:
        """记录最终统计信息"""
        elapsed_time = time.time() - self._start_time
        
        if elapsed_time > 0:
            avg_fps = self._frame_count / elapsed_time
            success_rate = (self._successful_reads / (self._successful_reads + self._total_failures)) * 100 if (self._successful_reads + self._total_failures) > 0 else 0
            
            logger.info(f"生产者最终统计 [{self.stream_key}] - "
                       f"运行时长: {elapsed_time:.1f}秒, "
                       f"总帧数: {self._frame_count}, "
                       f"平均FPS: {avg_fps:.2f}, "
                       f"成功读取: {self._successful_reads}, "
                       f"总失败: {self._total_failures}, "
                       f"成功率: {success_rate:.1f}%")
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._running
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        current_time = time.time()
        elapsed_time = current_time - self._start_time if self._start_time > 0 else 0
        
        return {
            "stream_key": self.stream_key,
            "is_running": self._running,
            "frame_count": self._frame_count,
            "successful_reads": self._successful_reads,
            "total_failures": self._total_failures,
            "consecutive_failures": self._consecutive_failures,
            "elapsed_time": elapsed_time,
            "avg_fps": self._frame_count / elapsed_time if elapsed_time > 0 else 0,
            "last_frame_time": self._last_frame_time,
            "queue_size": self.frame_queue.get_queue_size()
        } 