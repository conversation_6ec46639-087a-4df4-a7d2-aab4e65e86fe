#!/usr/bin/env python
"""
专门调试test_super_resolution测试的脚本
"""
import os
import sys
import pytest
import logging
import numpy as np
from unittest.mock import MagicMock, patch

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

# 确保使用config_fake.yaml作为配置文件
os.environ['CONFIG_FILE'] = 'config.yaml'
logger.info(f"使用配置文件: {os.environ['CONFIG_FILE']}")

def debug_super_resolution_test():
    """调试超分辨率测试"""
    logger.info("开始调试超分辨率测试...")
    
    try:
        # 导入ImagePreprocessor类
        from app.processor.image_processor import ImagePreprocessor
        
        # 创建一个简单的测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        logger.info(f"测试图像形状: {test_image.shape}")
        
        # 创建ImagePreprocessor实例
        with patch('app.processor.image_processor.RealESRGANer'), \
             patch('app.processor.image_processor.RRDBNet'):
            processor = ImagePreprocessor()
            
            # 模拟upsampler
            processor.upsampler = MagicMock()
            processor.upsampler.enhance.return_value = (np.zeros((200, 200, 3), dtype=np.uint8), None)
            
            # 临时禁用target_size调整
            original_target_size = processor.target_size
            processor.target_size = None
            
            try:
                # 调用超分辨率方法
                result = processor._super_resolution(test_image)
                
                # 检查结果
                logger.info(f"超分辨率结果形状: {result.shape}")
                assert result.shape == (200, 200, 3), f"期望形状(200, 200, 3)，实际形状{result.shape}"
                
                # 验证upsampler.enhance被调用
                processor.upsampler.enhance.assert_called_once()
                
                logger.info("超分辨率测试通过")
            finally:
                # 恢复原始target_size
                processor.target_size = original_target_size
    except Exception as e:
        logger.error(f"超分辨率测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def run_super_resolution_test():
    """运行超分辨率测试"""
    logger.info("运行超分辨率测试...")
    
    # 获取测试文件路径
    test_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                            'unit', 'app', 'processor', 'test_image_processor.py')
    
    # 运行test_super_resolution测试
    logger.info("运行test_super_resolution测试...")
    result = pytest.main(["-xvs", f"{test_file}::test_super_resolution"])
    logger.info(f"test_super_resolution测试结果: {result}")

if __name__ == "__main__":
    logger.info("开始调试超分辨率测试...")
    
    # 直接调试超分辨率测试
    debug_super_resolution_test()
    
    # 使用pytest运行超分辨率测试
    run_super_resolution_test()
    
    logger.info("调试完成") 