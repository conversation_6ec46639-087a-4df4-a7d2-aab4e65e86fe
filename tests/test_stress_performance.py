import pytest
import asyncio
import numpy as np
import time
import threading
import gc
from unittest.mock import MagicMock, patch
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.async_frame_queue import AsyncFrameQueue
from app.utils.frame_data import FrameData
from app.video.streams.rtmp_connection_manager import RTMPConnectionManager
from app.video.streams.frame_producer import FrameProducer
from app.utils.component_monitor import ComponentMonitor
# from app.utils.stream_performance_monitor import StreamPerformanceMonitor, PerformanceMetrics


class TestStressAndPerformance:
    """压力测试和性能测试类"""
    
    @pytest.fixture
    def mock_rtmp_url(self):
        """模拟RTMP URL"""
        return "rtmp://stress-test:1935/live/performance-test"
    
    @pytest.mark.asyncio
    async def test_high_throughput_queue(self):
        """测试高吞吐量队列性能"""
        queue = AsyncFrameQueue(max_size=1000, drop_old_frames=True)
        
        # 生产者任务
        async def producer(producer_id, frame_count):
            frames_produced = 0
            for i in range(frame_count):
                frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
                frame_data = FrameData(
                    frame=frame,
                    timestamp=time.time() * 1000,
                    frame_id=f"{producer_id}_{i}",
                    metadata={"producer_id": producer_id}
                )
                
                success = await queue.put_frame(frame_data)
                if success:
                    frames_produced += 1
                
                # 高频率生产
                await asyncio.sleep(0.001)  # 1ms间隔
            
            return frames_produced
        
        # 消费者任务
        async def consumer(consumer_id, duration):
            frames_consumed = 0
            start_time = time.time()
            
            while time.time() - start_time < duration:
                frame_data = await queue.get_frame(timeout=0.1)
                if frame_data:
                    frames_consumed += 1
                await asyncio.sleep(0.001)
            
            return frames_consumed
        
        # 启动多个生产者和消费者
        start_time = time.time()
        
        tasks = []
        # 3个生产者，每个生产1000帧
        for i in range(3):
            tasks.append(producer(i, 1000))
        
        # 2个消费者，运行5秒
        for i in range(2):
            tasks.append(consumer(i, 5.0))
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 分析结果
        total_produced = sum(results[:3])  # 前3个是生产者结果
        total_consumed = sum(results[3:])  # 后2个是消费者结果
        
        production_rate = total_produced / duration
        consumption_rate = total_consumed / duration
        
        print(f"压力测试结果:")
        print(f"  运行时长: {duration:.2f}秒")
        print(f"  总生产帧数: {total_produced}")
        print(f"  总消费帧数: {total_consumed}")
        print(f"  生产速率: {production_rate:.1f} FPS")
        print(f"  消费速率: {consumption_rate:.1f} FPS")
        print(f"  最终队列大小: {queue.get_queue_size()}")
        
        # 性能断言
        assert production_rate > 100  # 至少100 FPS生产速率
        assert consumption_rate > 50   # 至少50 FPS消费速率
        assert total_consumed > 0      # 确保有帧被消费
    
    @pytest.mark.asyncio
    async def test_memory_stress(self):
        """测试内存压力下的表现"""
        queue = AsyncFrameQueue(max_size=500, drop_old_frames=True)
        
        # 生产大量中等尺寸帧
        async def memory_stress_producer():
            for i in range(1000):
                # 创建中等帧 (720p) - 减少内存压力
                frame = np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8)
                frame_data = FrameData(
                    frame=frame,
                    timestamp=time.time() * 1000,
                    frame_id=i,
                    metadata={"size": "720p"}
                )
                
                await queue.put_frame(frame_data)
                
                # 每100帧强制垃圾回收
                if i % 100 == 0:
                    gc.collect()
                    current_queue_size = queue.get_queue_size()
                    print(f"第{i}帧: 队列大小={current_queue_size}")
                
                await asyncio.sleep(0.01)
        
        # 慢速消费者
        async def slow_consumer():
            consumed = 0
            while consumed < 500:  # 只消费一部分
                frame_data = await queue.get_frame(timeout=1.0)
                if frame_data:
                    consumed += 1
                    # 检查帧完整性
                    assert frame_data.frame.shape == (720, 1280, 3)
                await asyncio.sleep(0.02)  # 慢于生产者
            return consumed
        
        # 运行内存压力测试
        start_memory = self._get_memory_usage()
        
        producer_task = asyncio.create_task(memory_stress_producer())
        consumer_task = asyncio.create_task(slow_consumer())
        
        # 等待完成
        consumed_count = await consumer_task
        
        # 取消生产者（如果还在运行）
        producer_task.cancel()
        try:
            await producer_task
        except asyncio.CancelledError:
            pass
        
        end_memory = self._get_memory_usage()
        
        print(f"内存压力测试结果:")
        print(f"  消费帧数: {consumed_count}")
        print(f"  开始内存: {start_memory:.1f}MB")
        print(f"  结束内存: {end_memory:.1f}MB")
        print(f"  内存增长: {end_memory - start_memory:.1f}MB")
        
        # 确保消费了足够的帧且内存增长合理
        assert consumed_count >= 500
        assert end_memory - start_memory < 200  # 内存增长不超过200MB
    
    def _get_memory_usage(self):
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0  # psutil未安装时返回0
    
    @pytest.mark.asyncio
    async def test_concurrent_streams_stress(self, mock_rtmp_url):
        """测试并发流压力"""
        num_streams = 5
        frames_per_stream = 200
        
        # 为每个流创建组件
        streams_data = []
        for i in range(num_streams):
            queue = AsyncFrameQueue(max_size=100, drop_old_frames=True)
            
            # 模拟连接管理器
            manager = RTMPConnectionManager(f"{mock_rtmp_url}_{i}")
            mock_cap = MagicMock()
            mock_cap.isOpened.return_value = True
            mock_cap.read.return_value = (True, np.zeros((480, 640, 3), dtype=np.uint8))
            manager._cap = mock_cap
            manager._is_connected = True
            
            producer = FrameProducer(
                connection_manager=manager,
                frame_queue=queue,
                stream_key=f"stress_stream_{i}"
            )
            
            streams_data.append({
                "queue": queue,
                "manager": manager,
                "producer": producer,
                "stream_id": i
            })
        
        # 启动所有生产者
        for stream_data in streams_data:
            stream_data["producer"].start_producing()
        
        # 为每个流创建消费者任务
        async def stream_consumer(stream_data, target_frames):
            consumed = 0
            queue = stream_data["queue"]
            stream_id = stream_data["stream_id"]
            
            while consumed < target_frames:
                frame_data = await queue.get_frame(timeout=1.0)
                if frame_data:
                    consumed += 1
                    # 验证帧数据完整性
                    assert frame_data.frame is not None
                    assert frame_data.timestamp > 0
                await asyncio.sleep(0.005)  # 200 FPS消费速率
            
            return stream_id, consumed
        
        # 运行并发消费
        start_time = time.time()
        
        consumer_tasks = [
            stream_consumer(stream_data, frames_per_stream)
            for stream_data in streams_data
        ]
        
        results = await asyncio.gather(*consumer_tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 停止所有生产者
        for stream_data in streams_data:
            stream_data["producer"].stop_producing()
        
        # 分析结果
        total_consumed = sum(result[1] for result in results)
        total_fps = total_consumed / duration
        
        print(f"并发流压力测试结果:")
        print(f"  流数量: {num_streams}")
        print(f"  运行时长: {duration:.2f}秒")
        print(f"  总消费帧数: {total_consumed}")
        print(f"  总体FPS: {total_fps:.1f}")
        print(f"  平均每流FPS: {total_fps/num_streams:.1f}")
        
        # 验证所有流都正常工作
        for stream_id, consumed in results:
            assert consumed >= frames_per_stream * 0.9  # 允许10%的损失
        
        assert total_fps > num_streams * 10  # 每个流至少10 FPS
    
    @pytest.mark.asyncio
    async def test_component_monitor_stress(self):
        """测试组件监控器压力"""
        monitor = ComponentMonitor()
        await monitor.start()
        
        num_components = 50
        state_changes_per_component = 100
        
        # 注册大量组件
        for i in range(num_components):
            monitor.register_component(f"stress_component_{i}")
        
        # 并发更新组件状态
        async def stress_component_updates(component_id, changes):
            from app.utils.component_monitor import ComponentState
            states = [ComponentState.RUNNING, ComponentState.ERROR, ComponentState.RECOVERING]
            
            for i in range(changes):
                state = states[i % len(states)]
                monitor.update_component_state(
                    f"stress_component_{component_id}",
                    state,
                    metadata={"update": i},
                    error_message=f"Error {i}" if state == ComponentState.ERROR else None
                )
                await asyncio.sleep(0.001)  # 高频率更新
        
        # 并发运行状态更新
        start_time = time.time()
        
        update_tasks = [
            stress_component_updates(i, state_changes_per_component)
            for i in range(num_components)
        ]
        
        await asyncio.gather(*update_tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 检查监控器统计
        stats = monitor.get_monitor_stats()
        
        print(f"组件监控器压力测试结果:")
        print(f"  组件数量: {num_components}")
        print(f"  每组件状态变更: {state_changes_per_component}")
        print(f"  运行时长: {duration:.2f}秒")
        print(f"  总状态变更: {num_components * state_changes_per_component}")
        print(f"  变更率: {(num_components * state_changes_per_component) / duration:.1f} 变更/秒")
        print(f"  最终统计: {stats}")
        
        # 验证监控器仍正常工作
        assert stats["total_components"] == num_components
        assert stats["total_components"] > 0
        
        await monitor.stop()
    
    # @pytest.mark.asyncio
    # async def test_performance_monitor_stress(self):
    #     """测试性能监控器压力"""
    #     perf_monitor = StreamPerformanceMonitor(metrics_window_size=100)
    #     await perf_monitor.start()
    #     
    #     num_streams = 20
    #     metrics_per_stream = 500
    #     
    #     # 注册多个流
    #     for i in range(num_streams):
    #         perf_monitor.register_stream(f"perf_stream_{i}")
    #     
    #     # 并发添加性能指标
    #     async def add_stream_metrics(stream_id, metric_count):
    #         for i in range(metric_count):
    #             metrics = PerformanceMetrics(
    #                 timestamp=time.time(),
    #                 fps=20.0 + np.random.normal(0, 5),  # 随机FPS
    #                 queue_size=int(50 + np.random.normal(0, 20)),
    #                 dropped_frames=int(max(0, np.random.normal(2, 1))),
    #                 memory_usage=60.0 + np.random.normal(0, 10),
    #                 cpu_usage=40.0 + np.random.normal(0, 15)
    #             )
    #             
    #             perf_monitor.add_metrics(f"perf_stream_{stream_id}", metrics)
    #             await asyncio.sleep(0.01)  # 100 Hz指标频率
    #     
    #     # 并发运行指标添加
    #     start_time = time.time()
    #     
    #     metric_tasks = [
    #         add_stream_metrics(i, metrics_per_stream)
    #         for i in range(num_streams)
    #     ]
    #     
    #     await asyncio.gather(*metric_tasks)
    #     
    #     end_time = time.time()
    #     duration = end_time - start_time
    #     
    #     # 检查性能监控器统计
    #     summary = perf_monitor.get_all_streams_summary()
    #     
    #     print(f"性能监控器压力测试结果:")
    #     print(f"  流数量: {num_streams}")
    #     print(f"  每流指标数: {metrics_per_stream}")
    #     print(f"  运行时长: {duration:.2f}秒")
    #     print(f"  总指标数: {num_streams * metrics_per_stream}")
    #     print(f"  指标添加率: {(num_streams * metrics_per_stream) / duration:.1f} 指标/秒")
    #     print(f"  监控统计: {summary}")
    #     
    #     # 验证性能监控器正常工作
    #     assert summary["total_streams"] == num_streams
    #     assert summary["health_rate"] >= 0
    #     
    #     # 检查个别流的指标
    #     for i in range(min(3, num_streams)):  # 检查前3个流
    #         stream_summary = perf_monitor.get_stream_metrics_summary(f"perf_stream_{i}")
    #         assert stream_summary is not None
    #         assert stream_summary["sample_count"] > 0
    #     
    #     await perf_monitor.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])  # -s 显示打印输出 