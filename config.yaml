# 运行模式配置
mode:
  # 运行模式: dev 或 prod
  run_mode: "prod"
  # 开发调试模式配置
  dev:
    enable_api: true
    enable_web: true
    port: 8000
  # 生产模式配置
  prod:
    enable_api: true
    enable_web: false
    port: 8001

# 数据存储配置
storage:
# 基础数据目录
  data_dir: ./app_data
  # 临时文件目录（固定不变）
  temp:
    base: ./app_data/temp
    videos: ./app_data/temp/videos
    reports: ./app_data/temp/reports
    images: ./app_data/temp/images
    
  # NFS服务器配置（固定不变）
  nfs:
    server: "***************"
    remote_paths:
      base: "/LgomDroneData"
      image: "/LgomDroneData/imageFile"
      video: "/LgomDroneData/videoFile"
      
  # 本地挂载点配置（根据环境变化）
  mount_points: 
    # 开发环境挂载点
    dev:
      base: ./app_data/LgomDroneData
      image: ./app_data/LgomDroneData/imageFile
      video: ./app_data/LgomDroneData/videoFile
      reports: ./app_data/LgomDroneData/reportFile
    # 生产环境挂载点
    prod:
      base: /mnt/LgomDroneData
      image: /mnt/LgomDroneData/imageFile
      video: /mnt/LgomDroneData/videoFile
      reports: ./app_data/reportFile
      
  # 数据保留策略
  retention:
    days: 7  # 临时文件保留天数
    cleanup_time: "00:00"  # 清理时间（24小时制）

# 日志配置
logging:
  level: DEBUG
  file:
    level: DEBUG
    path: ./app_data/logs/

# 视频流配置
video_stream:
  # 视频流类型: "webrtc" 或 "rtmp"
  stream_type: "rtmp"
  
  # WebRTC配置
  webrtc:
    base_url: "webrtc://192.168.100.192:1935/live/"  # WebRTC服务器基础地址
    ice_servers:
      - urls: ["stun:stun.l.google.com:19302"]
    # 可以添加更多WebRTC特定配置...
    
  # RTMP配置（保留但不使用）
  rtmp:
    base_url: "rtmp://192.168.100.192:1935/live/"
    
  # 通用配置
  read_fail_threshold: 30  # 读取失败阈值
  buffer_duration: 90  # 视频帧缓存时长（秒）
  detection_interval: 6  # 目标检测的时间间隔（秒）
  process_airport_stream: false  # 是否处理机场视频流
  frame_rate: 30  # 默认帧率
  opencv_ffmpeg_read_attempts: 20000  # OpenCV读取尝试次数，增加到20000以减少警告
  reconnect_interval: 5  # 视频流定期重连间隔（分钟）
  reset_threshold: 10  # 连续失败多少次后重置VideoCapture，降低阈值以更快响应流问题

# API配置
api:
  allow_origins:
    - http://localhost:8000
    - http://127.0.0.1:8000
    - "*"
  event_report:
    url: "http://***************:8082/webDroneClient/getDroneEventData"
    camera_type: "fly_camera"
    identify_type: 1
  video_save:
    url: "http://***************:8082/webDroneClient/receiveVideo"
  # HTTP请求超时配置
  http_timeout:
    total: 30      # 总超时时间（秒）
    connect: 10    # 连接超时时间（秒）
    sock_read: 15  # 读取超时时间（秒）
  # API安全配置
  security:
    api_key: "mnmwDKJ123MNoj"  # 请修改为强密码
    allowed_ips:  # 允许访问API的IP地址列表，为空则不限制IP
      - "127.0.0.1"
      - "*************"
      - "*************"
      # 添加更多允许的IP地址
    rate_limit:
      max_requests: 5  # 时间窗口内允许的最大请求数
      time_window: 60  # 时间窗口大小（秒）

# 模型配置
models:
  yolo:
    model_path: ./models/best_251018_5types.pt
  multimodal:
    ollama_url: http://*************:10434
    local_model: minicpm-v:8b
  super_resolution:
    model_scale: 2
    model_path: models/weights/RealESRGAN_x2.pth
    real_esrgan_path: /home/<USER>/softwares/develop/CV/lib/Real-ESRGAN

# 图像处理配置
image_processing:
  preprocess: false
  methods:
    - resize
    - normalize
  brightness_threshold: 50.0

# 数据库配置
database:
  host: ***************
  port: 3306
  user: root
  password: lgom
  database: dbs-tyb
  table: drone_info

# 无人机列表
drone_list:
  - drone_code: "1581F6Q8D24AH00G736E"
    site_name: "YongNingJiangKou2"
  - drone_code: "1581F6Q8D24AB00GJ00Q" 
    site_name: "PuQi1"
  - drone_code: "1581F6Q8X251A00G046J"
    site_name: "WenQiao2"
  # 注释掉的无人机可以按需添加
  # - drone_code: "1581F6Q8D245T00G6H88"
  #   site_name: "SiteName1"
  - drone_code: "1581F6Q8D245T00GU1GF" 
    site_name: "ChenXiDaQiao1"
  - drone_code: "1581F6Q8D248A00GCC85"
    site_name: "ChenNanFanShuiZhan2"
  - drone_code: "1581F6Q8D249300GHUJW"
    site_name: "ShangTangHe1"
  # - drone_code: "1581F6Q8D249300GSJJM"
  #   site_name: "SiteName5"
  - drone_code: "1581F6Q8D249300G99B4"
    site_name: "WuZhenBeiJiChang2"
  # - drone_code: "1581F6Q8D249300G6KBH"
  #   site_name: "SiteName7"
  - drone_code: "1581F6Q8D24AK00G4VJZ"
    site_name: "ShuiChe2"
  - drone_code: "1581F6Q8D249Q00G4PJ2"
    site_name: "FuJiaoDu1"
  - drone_code: "1581F6Q8D249Q00GJQ6Y"
    site_name: "WangJiaJing2"
  # - drone_code: "1581F6Q8D249300GW6RC"
  #   site_name: "SiteName11"
  - drone_code: "1581F6Q8D24AH00G00TT"
    site_name: "JinQingXinZha1"
  - drone_code: "1581F6Q8X24BD00G00MJ"
    site_name: "DeXingQiao2"
  # - drone_code: "1581F6Q8D24AE00G0186"
  #   site_name: "SiteName14"
  - drone_code: "1581F6Q8X24BD00G00PV"
    site_name: "LinChen1"
  - drone_code: "1581F6Q8D24AB00GDG3G"
    site_name: "ShangXianWu2"
  - drone_code: "1581F6Q8D24AB00GZ221"
    site_name: "WuZhen1"
  - drone_code: "1581F6Q8D249300G89YZ"
    site_name: "LangCaoGongLuQiao1"
  - drone_code: "1581F6Q8X251H00G04Z4"
    site_name: "SiZaoPu1"

# 流监控配置
stream_monitor:
  check_interval: 30  # 检查推流状态的间隔（秒）
  retry_times: 2      # 重试次数
  retry_interval: 5   # 重试间隔（秒）
  only_monitor_config_drones: true  # 是否只监控配置文件中指定的无人机

# 调度器配置
scheduler:
  reconnect_interval: 300  # 重连间隔（秒，5分钟）
  status_check_interval: 60  # 状态检查间隔（秒，1分钟）

# 目标识别配置
detection:
  # 是否检查距离
  check_distance: true
  # 需要检测的目标ID列表
  target_ids: ["1", "2", "3", "4", "5", "6"]
  targets:
    - id: 1
      name: "船只"
      category: "水域安全"
      keyword: "boat"  # 用于模型识别的关键词
      distance: 50  # 米为单位
      type: "drone_location"  # sampling_point 或 drone_location
      confidence_threshold: 0.6  # 置信度阈值
      
    - id: 2
      name: "人员"
      category: "人员安全"
      keyword: "person"
      distance: 50
      type: "drone_location"
      confidence_threshold: 0.5  # 人员检测需要更高的置信度

    - id: 3
      name: "疑似异常藻类"
      category: "水质安全"
      keyword: "algae"
      confidence_threshold: 0.5  # 藻类检测可以接受较低的置信度

    - id: 4
      name: "曝气"
      category: "水质安全"
      keyword: "water_aeration"
      confidence_threshold: 0.4
      
    - id: 5
      name: "施工"
      category: "环境安全"
      keyword: "construction"
      confidence_threshold: 0.65

    - id: 6
      name: "管路异常"
      category: "设施安全"
      keyword: "pipe"
      confidence_threshold: 0.55

# WebSocket配置
websocket:
  url: 'http://************:6789/'  # 基础URL，用于构建websocket和api地址
  auth:
    username: 'adminPC'
    password: 'adminPC'
    flag: 1
    token_refresh_time: '01:00'  # 每天凌晨1点更新token
  active_mode_codes: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18]  # 表示无人机活跃状态的mode_code列表
  # 重连配置
  reconnect:
    max_attempts: 10  # 最大重连尝试次数
    base_delay: 5     # 基础重连延迟（秒）
    max_delay: 300    # 最大重连延迟（秒）
    max_consecutive_failures: 3  # 最大连续失败次数
    health_check_interval: 30    # 健康检查间隔（秒）
    night_mode:
      start_hour: 22  # 夜间模式开始时间（小时）
      end_hour: 6     # 夜间模式结束时间（小时）
      reconnect_interval: 60  # 夜间重连间隔（秒）

# 新架构配置
# 帧队列配置
frame_queue:
  max_size: 100          # 队列最大大小
  timeout: 1.0           # 超时时间（秒）
  drop_old_frames: true  # 队列满时是否丢弃旧帧

# 生产者配置
producer:
  read_timeout: 5.0              # 读取超时时间（秒）
  max_consecutive_failures: 8    # 最大连续失败次数
  failure_sleep_time: 2.0        # 失败后休眠时间（秒）
  stats_interval: 10.0           # 统计信息输出间隔（秒）

# 连接管理器配置
connection_manager:
  health_check_interval: 30.0    # 健康检查间隔（秒）
  reconnect_max_attempts: 5      # 重连最大尝试次数
  reconnect_delay: 5.0           # 重连延迟时间（秒）
  open_timeout: 15000             # 连接打开超时时间（毫秒）
  read_timeout: 10000             # 连接读取超时时间（毫秒）
  buffer_size: 3                 # 连接缓冲区大小

# 组件监控配置
component_monitor:
  check_interval: 30.0           # 监控检查间隔（秒）
  max_error_count: 3             # 最大错误次数
  recovery_enabled: true         # 是否启用自动恢复