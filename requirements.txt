absl-py==2.1.0
addict==2.4.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aioice==0.9.0
aiortc==1.9.0
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.7.0
appdirs==1.4.4
APScheduler==3.11.0
argcomplete==3.5.3
asgiref==3.8.1
async-timeout==5.0.1
attr==0.3.1
attrs==24.3.0
autocommand==2.2.2
av==12.3.0
azure-core==1.32.0
azure-storage-blob==12.24.0
backports.tarfile==1.2.0
basicsr==1.4.2
black==24.10.0
bleach==5.0.1
boto==2.49.0
boto3==1.35.90
botocore==1.35.90
cachetools==5.5.0
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.8
colorama==0.4.6
contourpy==1.3.1
cryptography==44.0.0
cycler==0.12.1
datamodel-code-generator==0.26.1
defusedxml==0.7.1
Deprecated==1.2.15
distro==1.9.0
Django==4.2.17
django-annoying==0.10.6
django-cors-headers==3.6.0
django-csp==3.7
django-debug-toolbar==3.2.1
django-environ==0.10.0
django-extensions==3.2.3
django-filter==2.4.0
django-migration-linter==5.1.0
django-model-utils==4.1.1
django-ranged-fileresponse==0.1.2
django-rq==2.5.1
django-storages==1.12.3
django-user-agents==0.4.0
djangorestframework==3.15.2
dnspython==2.7.0
drf-dynamic-fields==0.3.0
drf-flex-fields==0.9.5
drf-generators==0.3.0
email_validator==2.2.0
exceptiongroup==1.2.2
expiringdict==1.2.2
facexlib==0.3.0
Faker==33.1.0
fastapi==0.115.6
ffmpeg-python==0.2.0
filelock==3.16.1
filterpy==1.4.5
fonttools==4.55.3
frozenlist==1.5.0
fsspec==2024.12.0
future==1.0.0
genson==1.3.0
gfpgan==1.3.8
google-api-core==2.24.0
google-auth==2.37.0
google-cloud-appengine-logging==1.5.0
google-cloud-audit-log==0.3.0
google-cloud-core==2.4.1
google-cloud-logging==3.11.3
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
grpc-google-iam-v1==0.13.1
grpcio==1.68.1
grpcio-status==1.68.1
h11==0.14.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
humansignal-drf-yasg==1.21.10.post1
idna==3.10
ifaddr==0.2.0
ijson==3.3.0
imageio==2.36.1
importlib_metadata==8.0.0
inflect==5.6.2
inflection==0.5.1
isodate==0.7.2
isort==5.13.2
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
Jinja2==3.1.5
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
jsf==0.11.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.7
label-studio-sdk==1.0.8
launchdarkly-server-sdk==8.2.1
lazy_loader==0.4
llvmlite==0.43.0
lmdb==1.5.1
lockfile==0.12.2
lxml==5.3.0
lxml_html_clean==0.4.1
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.0
mdurl==0.1.2
more-itertools==10.3.0
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.0.0
mysql-connector==2.2.9
networkx==3.4.2
nltk==3.9.1
numba==0.60.0
numpy==1.26.4
openai==1.58.1
opencv-python==4.10.0.84
opentelemetry-api==1.29.0
ordered-set==4.0.2
packaging==24.2
pandas==2.2.3
pathspec==0.12.1
pillow==11.0.0
platformdirs==4.3.6
propcache==0.2.1
proto-plus==1.25.0
protobuf==5.29.2
psutil==6.1.1
psycopg2-binary==2.9.10
py-cpuinfo==9.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pyboxen==1.3.0
pycparser==2.22
pydantic==2.10.4
pydantic_core==2.27.2
pyee==12.1.1
Pygments==2.18.0
pylibsrtp==0.10.0
pyOpenSSL==24.3.0
pyparsing==3.2.0
pyRFC3339==2.0.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==2.0.4
python-multipart==0.0.20
pytz==2022.7.1
PyYAML==6.0.2
-e ./lib/Real-ESRGAN
redis==3.5.3
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
requests-mock==1.12.1
rich==13.9.4
rpds-py==0.22.3
rq==1.10.1
rsa==4.9
rstr==3.2.2
rules==3.4
s3transfer==0.10.4
scikit-image==0.25.0
scipy==1.14.1
seaborn==0.13.2
semver==3.0.2
sentry-sdk==2.19.2
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
sqlparse==0.5.3
starlette==0.41.3
sympy==1.13.1
./lib/tb_nightly-2.19.0a20250207-py3-none-any.whl
tensorboard-data-server==0.7.2
tifffile==2024.12.12
toml==0.10.2
tomli==2.2.1
torch==2.4.1
-torchvision==0.19.1
+torchvision==0.15.2
tqdm==4.67.1
typeguard==4.3.0
typing_extensions==4.12.2
tzdata==2024.2
tzlocal==5.2
ua-parser==1.0.0
ua-parser-builtins==0.18.0.post1
ujson==5.10.0
ultralytics==8.3.53
ultralytics-thop==2.0.13
unicorn==2.1.1
uritemplate==4.1.1
urllib3==1.26.20
user-agents==2.2.0
uvicorn==0.34.0
watchfiles==1.0.3
webencodings==0.5.1
websockets==14.1
Werkzeug==3.1.3
wrapt==1.17.0
xmljson==0.2.1
yapf==0.43.0
yarl==1.18.3
zipp==3.19.2
