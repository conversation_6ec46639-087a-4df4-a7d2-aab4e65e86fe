import logging
import asyncio
import time
from typing import Optional, Any
from .base import BaseVideoStream
from .rtmp_connection_manager import RTMPConnectionManager
from .frame_producer import FrameProducer
from app.utils.config import config
from app.utils.async_frame_queue import AsyncFrameQueue
from app.utils.component_monitor import ComponentMonitor, ComponentState

logger = logging.getLogger(__name__)

class RTMPStream(BaseVideoStream):
    """RTMP视频流处理器"""
    
    def __init__(self):
        """初始化RTMP流"""
        super().__init__()
        self.rtmp_url: Optional[str] = None
        self.stream_key: Optional[str] = None
        self.connection_manager: Optional[RTMPConnectionManager] = None
        self.frame_queue: Optional[AsyncFrameQueue] = None
        self.frame_producer: Optional[FrameProducer] = None
        self.component_monitor: Optional[ComponentMonitor] = None
        self.is_running = False
        
        # 统计信息
        self.frame_count = 0
        self.start_time = 0.0
        
    async def start(self, stream_id: str) -> bool:
        """
        初始化RTMP流连接
        :param stream_id: 视频流ID
        :return: 是否成功初始化
        """
        try:
            # 构建RTMP URL和流标识符
            self.rtmp_url = f"{config.rtmp.base_url}{stream_id}-81-0-0"
            self.stream_key = f"{stream_id}_rtmp"
            logger.info(f"初始化RTMP流连接: {self.rtmp_url}")
            
            # 创建帧队列
            self.frame_queue = AsyncFrameQueue(
                max_size=config.FRAME_QUEUE_MAX_SIZE,
                drop_old_frames=config.FRAME_QUEUE_DROP_OLD_FRAMES
            )
            
            # 创建连接管理器
            self.connection_manager = RTMPConnectionManager(self.rtmp_url)
            
            # 创建组件监控器
            self.component_monitor = ComponentMonitor()
            
            # 创建帧生产者
            self.frame_producer = FrameProducer(
                connection_manager=self.connection_manager,
                frame_queue=self.frame_queue,
                stream_key=self.stream_key,
                on_error=self._on_producer_error
            )
            
            # 启动组件监控器
            await self.component_monitor.start()
            
            # 注册组件到监控器
            self.component_monitor.register_component(
                f"connection_{self.stream_key}",
                recovery_callback=self._recover_connection,
                shutdown_callback=self._shutdown_connection
            )
            
            self.component_monitor.register_component(
                f"producer_{self.stream_key}",
                recovery_callback=self._recover_producer,
                shutdown_callback=self._shutdown_producer
            )
            
            # 尝试建立连接
            connection_success = await self.connection_manager.create_connection()
            if not connection_success:
                logger.error(f"无法建立RTMP连接: {self.rtmp_url}")
                await self.stop()
                return False
            
            # 更新连接状态
            self.component_monitor.update_component_state(
                f"connection_{self.stream_key}",
                ComponentState.RUNNING
            )
            
            # 启动帧生产者
            producer_success = self.frame_producer.start_producing()
            if not producer_success:
                logger.error(f"无法启动帧生产者: {self.stream_key}")
                await self.stop()
                return False
            
            # 更新生产者状态
            self.component_monitor.update_component_state(
                f"producer_{self.stream_key}",
                ComponentState.RUNNING
            )
            
            self.is_running = True
            self.start_time = time.time()
            logger.info(f"成功启动RTMP流: {self.rtmp_url}")
            return True
            
        except Exception as e:
            logger.error(f"初始化RTMP流连接失败: {self.rtmp_url}, 错误: {str(e)}")
            await self.stop()
            return False
            
    async def stop(self) -> None:
        """停止RTMP视频流"""
        if not self.is_running:
            return
        
        logger.info(f"停止RTMP流: {self.rtmp_url}")
        self.is_running = False
        
        try:
            # 停止帧生产者
            if self.frame_producer:
                self.frame_producer.stop_producing()
                
            # 关闭连接管理器
            if self.connection_manager:
                await self.connection_manager.close_connection()
                
            # 停止组件监控器
            if self.component_monitor:
                await self.component_monitor.stop()
                
            # 关闭帧队列
            if self.frame_queue:
                await self.frame_queue.close()
                
            # 输出最终统计信息
            if self.frame_producer:
                stats = self.frame_producer.get_stats()
                logger.info(f"RTMP流最终统计: {stats}")
                
        except Exception as e:
            logger.error(f"停止RTMP流时出错: {self.rtmp_url}, 错误: {str(e)}")
        
        logger.info(f"已停止RTMP流: {self.rtmp_url}")
        
    async def check_stream(self) -> bool:
        """
        检查RTMP流是否活跃
        :return: 是否活跃
        """
        if not self.is_running:
            return False
        
        try:
            # 检查组件状态
            if not self.component_monitor:
                return False
                
            # 检查连接和生产者状态
            connection_healthy = self.component_monitor.is_component_healthy(f"connection_{self.stream_key}")
            producer_healthy = self.component_monitor.is_component_healthy(f"producer_{self.stream_key}")
            
            # 检查连接管理器健康状态
            connection_health = False
            if self.connection_manager:
                connection_health = await self.connection_manager.check_health()
                
            # 更新组件状态
            if connection_health:
                self.component_monitor.update_component_state(
                    f"connection_{self.stream_key}",
                    ComponentState.RUNNING
                )
            else:
                self.component_monitor.update_component_state(
                    f"connection_{self.stream_key}",
                    ComponentState.ERROR,
                    error_message="连接健康检查失败"
                )
            
            # 检查生产者状态
            if self.frame_producer and self.frame_producer.is_running():
                self.component_monitor.update_component_state(
                    f"producer_{self.stream_key}",
                    ComponentState.RUNNING
                )
            else:
                self.component_monitor.update_component_state(
                    f"producer_{self.stream_key}",
                    ComponentState.ERROR,
                    error_message="生产者未运行"
                )
            
            # 流健康当且仅当连接和生产者都健康
            is_healthy = connection_health and self.frame_producer and self.frame_producer.is_running()
            
            if is_healthy:
                logger.debug(f"RTMP流检查成功: {self.rtmp_url}")
            else:
                logger.warning(f"RTMP流检查失败: {self.rtmp_url}")
                
            return is_healthy
                
        except Exception as e:
            logger.error(f"检查RTMP流时出错: {self.rtmp_url}, 错误: {str(e)}")
            return False
        
    async def get_frame(self) -> Optional[Any]:
        """
        从帧队列获取视频帧
        :return: 视频帧，如果读取失败则返回None
        """
        if not self.is_running or not self.frame_queue:
            return None
        
        try:
            # 从帧队列获取帧数据
            frame_data = await self.frame_queue.get_frame(timeout=config.FRAME_QUEUE_TIMEOUT)
            
            if frame_data is None:
                return None
            
            # 更新帧计数
            self.frame_count += 1
            
            # 定期输出统计信息和收集性能指标
            if self.frame_count % 100 == 0:
                queue_stats = self.frame_queue.get_stats()
                logger.debug(f"获取RTMP帧成功: {self.rtmp_url}, "
                           f"帧计数: {self.frame_count}, "
                           f"队列大小: {self.frame_queue.get_queue_size()}, "
                           f"丢帧数: {queue_stats.dropped_frames}")
                
                # 收集性能指标
                # if self.performance_monitor and self.frame_producer:
                #     current_time = time.time()
                #     elapsed_time = current_time - self.start_time
                #     current_fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
                    
                #     metrics = PerformanceMetrics(
                #         timestamp=current_time,
                #         fps=current_fps,
                #         queue_size=self.frame_queue.get_queue_size(),
                #         dropped_frames=queue_stats.dropped_frames,
                #         memory_usage=0.0,  # 将由监控器更新
                #         cpu_usage=0.0      # 将由监控器更新
                #     )
                    
                #     self.performance_monitor.add_metrics(self.stream_key, metrics)
            
            return frame_data.frame
                
        except Exception as e:
            logger.error(f"获取RTMP帧时出错: {self.rtmp_url}, 错误: {str(e)}")
            return None
            
    def _on_producer_error(self, error: Exception):
        """处理生产者错误的回调函数"""
        logger.error(f"生产者错误: {self.stream_key}, 错误: {str(error)}")
        
        # 更新组件状态
        if self.component_monitor:
            self.component_monitor.update_component_state(
                f"producer_{self.stream_key}",
                ComponentState.ERROR,
                error_message=str(error)
            )
    
    async def _recover_connection(self) -> bool:
        """恢复连接的回调函数"""
        try:
            logger.info(f"尝试恢复连接: {self.stream_key}")
            
            if not self.connection_manager:
                return False
                
            # 尝试重新连接
            success = await self.connection_manager.reconnect()
            
            if success:
                logger.info(f"连接恢复成功: {self.stream_key}")
                return True
            else:
                logger.error(f"连接恢复失败: {self.stream_key}")
                return False
                
        except Exception as e:
            logger.error(f"恢复连接时出错: {self.stream_key}, 错误: {str(e)}")
            return False
    
    async def _shutdown_connection(self):
        """关闭连接的回调函数"""
        try:
            logger.info(f"关闭连接: {self.stream_key}")
            
            if self.connection_manager:
                await self.connection_manager.close_connection()
                
        except Exception as e:
            logger.error(f"关闭连接时出错: {self.stream_key}, 错误: {str(e)}")
    
    async def _recover_producer(self) -> bool:
        """恢复生产者的回调函数"""
        try:
            logger.info(f"尝试恢复生产者: {self.stream_key}")
            
            if not self.frame_producer:
                return False
            
            # 如果生产者未运行，尝试重新启动
            if not self.frame_producer.is_running():
                success = self.frame_producer.start_producing()
                
                if success:
                    logger.info(f"生产者恢复成功: {self.stream_key}")
                    return True
                else:
                    logger.error(f"生产者恢复失败: {self.stream_key}")
                    return False
            else:
                # 生产者已在运行
                return True
                
        except Exception as e:
            logger.error(f"恢复生产者时出错: {self.stream_key}, 错误: {str(e)}")
            return False
    
    async def _shutdown_producer(self):
        """关闭生产者的回调函数"""
        try:
            logger.info(f"关闭生产者: {self.stream_key}")
            
            if self.frame_producer:
                self.frame_producer.stop_producing()
                
        except Exception as e:
            logger.error(f"关闭生产者时出错: {self.stream_key}, 错误: {str(e)}")
    
    def get_stream_stats(self) -> dict:
        """获取流统计信息"""
        stats = {
            "stream_key": self.stream_key,
            "rtmp_url": self.rtmp_url,
            "is_running": self.is_running,
            "frame_count": self.frame_count,
            "start_time": self.start_time
        }
        
        # 添加生产者统计
        if self.frame_producer:
            stats["producer"] = self.frame_producer.get_stats()
            
        # 添加队列统计
        if self.frame_queue:
            queue_stats = self.frame_queue.get_stats()
            stats["queue"] = {
                "size": self.frame_queue.get_queue_size(),
                "dropped_frames": queue_stats.dropped_frames,
                "total_frames": queue_stats.total_frames
            }
            
        # 添加连接统计
        if self.connection_manager:
            stats["connection"] = self.connection_manager.get_connection_info()
            
        # 添加组件监控统计
        if self.component_monitor:
            stats["monitor"] = self.component_monitor.get_monitor_stats()
            
        # 添加性能监控统计
        # if self.performance_monitor:
        #     stats["performance"] = self.performance_monitor.get_stream_metrics_summary(self.stream_key)
            
        return stats 