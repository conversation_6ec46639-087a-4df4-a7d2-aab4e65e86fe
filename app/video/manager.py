import logging
import asyncio
from typing import Dict, Optional, List, Set
from .streams.base import BaseVideoStream
from .streams.rtmp import RTMPStream
from .streams.webrtc import WebRTCStream
from app.database.drone_info import DroneInfoManager
from app.utils.shared_state import active_drones
from app.database.websocket_client import WebSocketClient

logger = logging.getLogger(__name__)

class VideoStreamManager:
    """视频流统一管理器"""
    
    def __init__(self, drone_info_manager: DroneInfoManager):
        """初始化视频流管理器"""
        self.streams: Dict[str, BaseVideoStream] = {}
        self.drone_info_manager = drone_info_manager
        self.websocket_client = None
        self.stream_type = None
        
    def set_websocket_client(self, websocket_client: WebSocketClient):
        """设置WebSocket客户端"""
        self.websocket_client = websocket_client
        
    def _create_stream(self) -> BaseVideoStream:
        """创建视频流实例"""
        if self.stream_type == "webrtc":
            return WebRTCStream()
        return RTMPStream()  # 默认使用RTMP
        
    async def get_stream(self, drone_code: str, stream_type: str = "drone") -> Optional[BaseVideoStream]:
        """
        获取指定无人机的视频流
        :param drone_code: 无人机编号
        :param stream_type: 流类型，"drone"或"airport"
        :return: 视频流对象
        """
        try:
            # 获取无人机信息
            drone_info = self.drone_info_manager.get_drone_info(drone_code)
            if not drone_info:
                logger.warning(f"未找到无人机 {drone_code} 的信息")
                return None
                
            stream_key = f"{drone_code}_{stream_type}"
            if stream_key not in self.streams:
                stream = self._create_stream()
                video_code = (drone_info.drone_video_code 
                            if stream_type == "drone" 
                            else drone_info.airport_video_code)
                
                logger.info(f"尝试启动视频流: {drone_code}, 视频编码: {video_code}")
                if await stream.start(video_code):
                    self.streams[stream_key] = stream
                    logger.info(f"成功启动视频流: {drone_code}, 视频编码: {video_code}")
                else:
                    logger.warning(f"启动视频流失败: {drone_code}, 视频编码: {video_code}")
                    
            return self.streams.get(stream_key)
            
        except Exception as e:
            logger.error(f"获取视频流失败: {str(e)}")
            return None
            
    async def cleanup(self):
        """清理所有视频流资源"""
        for stream in self.streams.values():
            await stream.stop()
        self.streams.clear()
        
    async def close_all_streams(self):
        """关闭所有视频流，但保留流对象的引用，以便后续重新初始化"""
        logger.info(f"关闭所有视频流，共 {len(self.streams)} 个")
        for stream_key, stream in self.streams.items():
            try:
                logger.info(f"关闭视频流: {stream_key}")
                await stream.stop()
            except Exception as e:
                logger.error(f"关闭视频流 {stream_key} 时出错: {str(e)}")
        
        # 清空流字典，但不调用clear()，这样可以保留键值对的引用
        self.streams = {}
        logger.info("所有视频流已关闭") 