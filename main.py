import logging
import os  # 添加 os 模块
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from app.api.detect import router as detect_router
from app.services.monitor_service import MonitorService
from app.utils.config import config
from app.utils.logger import setup_logger
from contextlib import asynccontextmanager

from fastapi.middleware.cors import CORSMiddleware
import asyncio
import uvicorn
from app.utils.nfs_manager import nfs_manager
from app.utils.app_state import app_state

# 设置 OpenCV 环境变量，增加读取尝试次数
os.environ['OPENCV_FFMPEG_READ_ATTEMPTS'] = str(config.OPENCV_FFMPEG_READ_ATTEMPTS)
print(f"设置 OPENCV_FFMPEG_READ_ATTEMPTS = {config.OPENCV_FFMPEG_READ_ATTEMPTS}")

# 设置根日志器
logger = setup_logger(__name__, 
    console_level=getattr(logging, config.LOG_LEVEL),
    file_level=getattr(logging, config.LOG_FILE_LEVEL)
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    try:
        # 初始化NFS挂载
        if not nfs_manager.setup():
            logger.error("NFS挂载初始化失败")   
            return
            
        # 启动监控服务
        monitor_service = MonitorService()
        # 使用asyncio创建后台任务并注册到应用状态管理器
        monitor_task = asyncio.create_task(monitor_service.start())
        app_state.register_task(monitor_task)
        
        # 注册清理回调
        app_state.register_cleanup_callback(monitor_service.stop)
        app_state.register_cleanup_callback(nfs_manager.cleanup)
        
        logger.debug(f"应用以{config.RUN_MODE}模式启动")
    except Exception as e:
        logger.error(f"启动监控服务失败: {str(e)}", exc_info=True)
    
    yield
    
    # 关闭时执行 - 现在由应用状态管理器处理
    logger.info("应用关闭，清理工作由应用状态管理器处理")

app = FastAPI(lifespan=lifespan)

# 根据运行模式配置应用
if config.ENABLE_API:
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.ALLOW_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

if config.ENABLE_WEB:
    # 挂载静态文件
    app.mount("/static", StaticFiles(directory="app/static"), name="static")
    # 模板配置
    templates = Jinja2Templates(directory="app/templates")
    logger.info(f"Web界面已启用")
    
    @app.get("/")
    def read_root():
        return templates.TemplateResponse("index.html", {"request": {}})

if config.ENABLE_API:
    # 包含API路由
    app.include_router(detect_router, prefix="/api", tags=["Object Detection"])
    logger.info(f"API路由已启用，可通过 /api 前缀访问")
    logger.info(f"模型热更新API端点: /api/reload_model")

async def main():
    """主程序入口"""
    try:
        # 启动应用状态管理器
        app_state.start()
        
        # 初始化NFS挂载
        if not nfs_manager.setup():
            logger.error("NFS挂载初始化失败")
            return
            
        # 创建监控服务实例
        monitor_service = MonitorService()
        
        # 在后台启动监控服务并注册到应用状态管理器
        monitor_task = asyncio.create_task(monitor_service.start())
        app_state.register_task(monitor_task)
        
        # 注册清理回调
        app_state.register_cleanup_callback(monitor_service.stop)
        app_state.register_cleanup_callback(nfs_manager.cleanup)
        
        logger.info("监控服务已在后台启动")
        
        # 启动Web服务器
        config_port = config.PORT
        logger.info(f"准备启动Web服务器，监听端口: {config_port}")
        
        try:
            # 直接启动Web服务器
            uvicorn_config = uvicorn.Config(app, host="0.0.0.0", port=config_port, log_level="info")
            server = uvicorn.Server(uvicorn_config)
            await server.serve()
            
        except Exception as e:
            logger.error(f"启动Web服务器失败: {str(e)}")
            import traceback
            logger.error(f"Web服务器启动异常详情: {traceback.format_exc()}")
            
    except KeyboardInterrupt:
        logger.info("接收到停止信号，正在关闭服务...")
        # 在事件循环关闭前执行优雅退出
        if app_state.is_running:
            await app_state._graceful_shutdown()
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        logger.exception("详细错误信息:")
        # 异常情况下也执行优雅退出
        if app_state.is_running:
            await app_state._graceful_shutdown()
    finally:
        # 确保应用状态管理器停止
        app_state.stop()
        
if __name__ == "__main__":
    asyncio.run(main())

