# 单元测试

本目录包含项目的单元测试。

## 测试结构

- `unit/`: 单元测试目录
  - `test_init.py`: 基本环境测试
  - `app/`: 应用程序模块测试
    - `utils/`: 工具模块测试
      - `test_event_reporter.py`: EventReporter类的测试
    - `processor/`: 处理器模块测试
      - `test_image_processor.py`: ImagePreprocessor类的测试

## 运行测试

### 运行所有测试

```bash
# 从项目根目录运行
python tests/run_tests.py

# 或者使用pytest直接运行
pytest -xvs tests/unit
```

### 运行特定模块的测试

```bash
# 使用run_tests.py运行特定模块的测试
python tests/run_tests.py --module event_reporter
python tests/run_tests.py --module image_processor

# 使用专门的脚本运行event_reporter测试
python tests/test_event_reporter.py

# 运行event_reporter中的特定测试函数
python tests/test_event_reporter.py --test test_save_detection_video
```

### 运行特定测试文件

```bash
# 运行特定测试文件
pytest -xvs tests/unit/app/utils/test_event_reporter.py
pytest -xvs tests/unit/app/processor/test_image_processor.py
```

### 运行特定测试函数

```bash
# 运行特定测试函数
pytest -xvs tests/unit/app/utils/test_event_reporter.py::test_save_detection_video
pytest -xvs tests/unit/app/processor/test_image_processor.py::test_super_resolution
```

## 测试依赖

测试需要以下依赖：
- pytest
- pytest-asyncio (用于测试异步函数)

可以通过以下命令安装：

```bash
pip install pytest pytest-asyncio
```

## 日志和调试

测试代码中添加了详细的日志记录，以帮助调试测试过程中的问题。日志级别默认设置为 INFO。

### 查看更详细的日志

如果需要查看更详细的日志，可以设置环境变量 `PYTEST_LOG_LEVEL=DEBUG`：

```bash
PYTEST_LOG_LEVEL=DEBUG python tests/run_tests.py
```

或者直接使用 pytest 的 `-v` 或 `-vv` 参数：

```bash
pytest -vv tests/unit
```

### 调试特定测试

提供了几个专门的调试脚本：

1. **环境检查脚本**：检查测试环境是否正确设置
   ```bash
   python tests/check_env.py
   ```

2. **通用调试脚本**：提供详细的调试信息
   ```bash
   python tests/debug_tests.py
   ```

3. **ImageProcessor调试脚本**：专门调试ImageProcessor测试
   ```bash
   python tests/debug_image_processor.py
   ```

4. **超分辨率测试调试脚本**：专门调试超分辨率测试
   ```bash
   python tests/debug_super_resolution.py
   ```

5. **EventReporter测试脚本**：专门运行EventReporter测试
   ```bash
   python tests/test_event_reporter.py
   ```

### 常见问题排查

1. **找不到测试目录**：确保在项目根目录下运行测试命令，或者使用绝对路径指定测试目录。

2. **导入错误**：检查 `conftest.py` 中的路径设置，确保项目根目录已添加到 Python 路径中。

3. **配置文件错误**：确保 `config_fake.yaml` 文件存在并包含必要的配置项。

4. **测试依赖缺失**：确保已安装所有必要的测试依赖。

5. **Mock对象问题**：如果测试断言失败，检查是否正确模拟了被测试对象的属性和方法。特别是对于布尔值属性，确保使用实际的布尔值而不是MagicMock对象。

6. **图像尺寸问题**：在超分辨率测试中，确保模拟的超分辨率模型返回正确尺寸的图像。测试期望输出图像尺寸为输入的2倍。

## 注意事项

1. 测试使用 `config_fake.yaml` 作为配置文件，确保该文件存在并包含必要的配置项。
2. 测试使用模拟对象（mock）来隔离被测试的代码，避免对实际系统产生影响。
3. 异步函数的测试使用 `@pytest.mark.asyncio` 装饰器。 