import logging
import aiohttp
import os
from typing import Optional
from app.utils.config import config
from app.utils.file_manager import file_manager
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

class EventReporter:
    def __init__(self):
        """初始化事件上报器"""
        self.event_api_url = config.EVENT_REPORT_URL  # getDroneEventData接口
        self.video_api_url = config.VIDEO_SAVE_URL    # receiveVideo接口
        self.camera_type = config.EVENT_REPORT_CAMERA_TYPE
        self.identify_type = config.EVENT_REPORT_IDENTIFY_TYPE
        
        # 设置HTTP请求超时配置
        self.timeout = aiohttp.ClientTimeout(
            total=config.HTTP_TIMEOUT_TOTAL,      # 总超时时间
            connect=config.HTTP_TIMEOUT_CONNECT,  # 连接超时
            sock_read=config.HTTP_TIMEOUT_SOCK_READ  # 读取超时
        )
        
        # 记录API URL信息
        logger.info(f"事件上报URL: {self.event_api_url}")
        logger.info(f"视频保存URL: {self.video_api_url}")
        logger.info(f"HTTP请求超时配置: 总超时={self.timeout.total}s, 连接超时={self.timeout.connect}s, 读取超时={self.timeout.sock_read}s")
        
        # 初始化文件管理器
        self.file_manager = file_manager
        
        # 获取DroneStreamManager实例
        from app.database.drone_info import DroneInfoManager
        self.drone_manager = DroneInfoManager()

    async def report_event(self, result: dict, stream_processor) -> bool:
        """
        上报检测事件
        :param result: 检测结果
        :param stream_processor: StreamProcessor实例
        :return: 上报是否成功
        """
        try:
            drone_code = result["drone_code"]
            timestamp = result["timestamp"]
            detection_result = result["result"]
            video_path = result.get("video_path", "")  # 从result中获取视频路径
            
            # 检查结果是否有效
            if not detection_result or not isinstance(detection_result, dict):
                logger.warning(f"检测结果无效或格式不正确: {type(detection_result)}")
                return False
                
            # 获取无人机信息
            drone_info = self.drone_manager.get_drone_info(drone_code)
            if not drone_info:
                logger.error(f"未找到无人机 {drone_code} 的信息")
                return False
                
            # 记录无人机位置信息，用于调试
            # logger.debug(f"无人机 {drone_code} 位置信息: 经度={drone_info.longitude}, 纬度={drone_info.latitude}")
            
            # 由于使用了单例模式，这里的drone_info应该已经是最新的
            # 但为了确保，我们再次检查位置信息是否有效
            if drone_info.longitude == 0.0 or drone_info.latitude == 0.0:
                logger.warning(f"无人机 {drone_code} 位置信息无效: 经度={drone_info.longitude}, 纬度={drone_info.latitude}")
                # 尝试从共享状态中获取位置信息
                try:
                    from app.utils.shared_state import active_drones
                    if drone_code in active_drones:
                        logger.info(f"尝试从共享状态获取无人机位置信息: {active_drones[drone_code]}")
                except Exception as e:
                    logger.warning(f"尝试从共享状态获取位置信息失败: {str(e)}")
            
            # 如果未配置事件上报URL，则跳过上报
            if not self.event_api_url:
                logger.warning("事件上报URL未配置，跳过事件上报")
                return False
                
            # 对每个检测到的目标分别上报事件
            detections = detection_result.get("detections", [])
            if not detections:
                logger.warning(f"未检测到目标: {drone_code}")
                return False
                
            logger.info(f"检测到 {len(detections)} 个目标为{detections}，准备上报")
            
            # 收集所有检测到的目标名称，用于设置business_type
            target_names = []
            for detection in detections:
                target_name = detection.get("target_name")
                if target_name and target_name not in target_names:
                    target_names.append(target_name)
            
            # 将所有目标名称用逗号连接
            business_type = "，".join(target_names) if target_names else "船只"
            logger.info(f"设置business_type为: {business_type}")
            
            # 跟踪是否至少有一个目标成功上报
            any_success = False
            
            for detection in detections:
                # 获取图片路径
                init_image_path = detection_result.get("image_paths", {}).get("init", "")
                res_image_path = detection_result.get("image_paths", {}).get("res", "")
                
                # 只使用文件名而不是完整路径
                init_image_filename = os.path.basename(init_image_path) if init_image_path else ""
                res_image_filename = os.path.basename(res_image_path) if res_image_path else ""
                video_filename = os.path.basename(video_path) if video_path else ""

                logger.debug(f"检查无人机{drone_info.drone_code} 位置信息: 经度={drone_info.longitude}, 纬度={drone_info.latitude}")
                
                # 构建事件数据 (getDroneEventData接口)
                event_data = {
                    "imageinit": init_image_filename,  # 初始图片文件名
                    "imageres": res_image_filename,  # 标注后图片文件名
                    "timestamp": str(int(timestamp)),  # 时间戳
                    "err": detection.get("error_code", "2"),  # 错误码
                    "camera": "fly_camera",  # 相机类型
                    "snNum": drone_info.drone_code,  # 无人机编码
                    "business_type": business_type,  # 业务类型，使用所有检测到的目标名称
                    "identifyType": str(self.identify_type),  # 识别类型
                }
                
                # 尝试从不同来源获取经纬度
                longitude = 0.0
                latitude = 0.0
                
                # 1. 首先尝试从drone_info对象获取
                if drone_info.longitude != 0.0 and drone_info.latitude != 0.0:
                    longitude = drone_info.longitude
                    latitude = drone_info.latitude
                    logger.debug(f"使用drone_info中的位置: 经度={longitude}, 纬度={latitude}")
                # 2. 如果drone_info中没有，尝试从共享状态获取
                else:
                    try:
                        from app.utils.shared_state import active_drones
                        if drone_code in active_drones and 'longitude' in active_drones[drone_code] and 'latitude' in active_drones[drone_code]:
                            longitude = active_drones[drone_code]['longitude']
                            latitude = active_drones[drone_code]['latitude']
                            logger.info(f"使用共享状态中的位置: 经度={longitude}, 纬度={latitude}")
                    except Exception as e:
                        logger.warning(f"从共享状态获取位置失败: {str(e)}")
                
                # 添加经纬度到事件数据
                event_data["longitude"] = str(longitude)
                event_data["latitude"] = str(latitude)
                
                # 记录最终上报的经纬度
                logger.debug(f"最终上报的经纬度: 经度={event_data['longitude']}, 纬度={event_data['latitude']}")
                
                # 调用getDroneEventData接口
                try:
                    async with aiohttp.ClientSession(timeout=self.timeout) as session:
                        logger.info(f"正在调用事件上报接口: {self.event_api_url}")
                        logger.debug(f"上报事件数据: {event_data}")
                        # 使用form-data格式而不是json格式
                        async with session.post(self.event_api_url, data=event_data) as response:
                            response_text = await response.text()
                            if response.status != 200:
                                logger.error(f"上报事件失败: HTTP状态码 {response.status}, 响应: {response_text}")
                                logger.error(f"请求URL: {self.event_api_url}")
                                logger.error(f"请求数据: {event_data}")
                                # 单个目标上报失败，继续处理下一个目标
                                event_success = False
                            else:
                                logger.info(f"成功上报事件: {drone_code}, 目标类型: {detection.get('target_category', '未知')}")
                                # logger.debug(f"上报事件响应: {response_text}")
                                event_success = True
                                any_success = True
                except Exception as e:
                    logger.error(f"调用事件上报接口失败: {str(e)}")
                    logger.error(f"请求URL: {self.event_api_url}")
                    import traceback
                    logger.error(f"调用事件上报接口异常详情: {traceback.format_exc()}")
                    # 单个目标上报失败，继续处理下一个目标
                    event_success = False
                
                # 如果当前目标上报失败，继续处理下一个目标
                if not event_success:
                    continue
                    
            # 调用receiveVideo接口上报视频信息
            if self.video_api_url and video_path:
                # 只使用文件名而不是完整路径
                video_filename = os.path.basename(video_path)
                logger.info(f"准备上报视频信息: {video_filename}, 完整路径: {video_path}")
                await self._report_video_info(video_filename, drone_info.drone_code, timestamp)
            else:
                if not self.video_api_url:
                    logger.warning("视频上传URL未配置，跳过视频上传")
                else:
                    logger.warning("视频文件路径为空，跳过视频上传")
            
            # 如果至少有一个目标成功上报，则认为上报成功
            return any_success
            
        except Exception as e:
            logger.error(f"上报事件失败: {str(e)}")
            import traceback
            logger.error(f"上报事件异常详情: {traceback.format_exc()}")
            return False

    async def _report_video_info(self, video_filename: str, sn_num: str, timestamp: float) -> None:
        """
        调用receiveVideo接口上报视频信息（只上报视频名称，不上传视频文件）
        :param video_filename: 视频文件名（不是完整路径）
        :param sn_num: 无人机编号
        :param timestamp: 时间戳
        """
        try:
            # 检查视频文件是否存在
            # 首先尝试在当前目录查找
            if not os.path.exists(video_filename):
                # 如果当前目录不存在，尝试在视频临时目录查找
                date_str = datetime.fromtimestamp(timestamp / 1000.0).strftime('%Y%m%d')
                video_dir = os.path.join(self.file_manager.temp_dirs['videos'], date_str)
                full_path = os.path.join(video_dir, video_filename)
                
                if not os.path.exists(full_path):
                    # 尝试使用绝对路径
                    abs_video_dir = os.path.abspath(video_dir)
                    abs_path = os.path.join(abs_video_dir, video_filename)
                    
                    if not os.path.exists(abs_path):
                        logger.warning(f"视频文件不存在: {video_filename}")
                        logger.warning(f"尝试的路径: 当前目录, {full_path}, {abs_path}")
                        # 继续执行，因为我们只需要上报文件名
            
            # 构建上传数据 (receiveVideo接口)
            data = {
                "video": video_filename,  # 视频文件名
                "snNum": sn_num,  # 无人机编号
                "timestamp": str(int(timestamp)),  # 时间戳
                "identifyType": str(self.identify_type)  # 识别类型，使用与事件上报相同的类型
            }
            
            # logger.info(f"正在调用视频信息上报接口: {self.video_api_url}")
            logger.debug(f"上报视频信息: {data}")
                
            # 调用receiveVideo接口
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                # logger.info(f"正在调用视频信息上报接口: {self.video_api_url}")
                logger.debug(f"上报视频信息: {data}")
                # 使用form-data格式而不是json格式
                async with session.post(self.video_api_url, data=data) as response:
                    response_text = await response.text()
                    if response.status != 200:
                        logger.error(f"上报视频信息失败: HTTP状态码 {response.status}, 响应: {response_text}")
                        logger.error(f"请求URL: {self.video_api_url}")
                        logger.error(f"请求数据: {data}")
                    else:
                        logger.info(f"成功上报视频信息: {sn_num}, 视频文件名: {video_filename}")
                        # logger.debug(f"上报视频信息响应: {response_text}")
                            
        except Exception as e:
            logger.error(f"上报视频信息失败: {str(e)}")
            import traceback
            logger.error(f"上报视频信息异常详情: {traceback.format_exc()}")

# 创建全局实例
event_reporter = EventReporter() 