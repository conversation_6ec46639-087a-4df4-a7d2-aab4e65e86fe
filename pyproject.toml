[project]
name = "cv"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "aiohttp>=3.12.13",
    "apscheduler>=3.11.0",
    "fastapi>=0.115.14",
    "lmdb>=1.6.2",
    "mysql-connector-python>=9.3.0",
    "opencv-python>=*********",
    "pillow>=11.2.1",
    "pyyaml>=6.0.2",
    "scikit-image>=0.25.2",
    "tensorboard>=2.19.0",
    "torch>=2.7.1",
    "torchaudio>=2.7.1",
    "torchvision>=0.22.1",
    "ultralytics>=8.3.160",
    "uvicorn[standard]>=0.35.0",
]

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true
