"""
EventReporter类的单元测试
"""
import pytest
import asyncio
from unittest.mock import MagicMock, patch, AsyncMock, call
import aiohttp
from datetime import datetime
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 导入被测试的类
from app.utils.event_reporter import EventReporter


@pytest.fixture
def event_reporter(mock_config, mock_file_manager):
    """创建EventReporter实例的fixture"""
    logger.info("设置 event_reporter fixture")
    with patch('app.utils.event_reporter.config', mock_config), \
         patch('app.utils.event_reporter.file_manager', mock_file_manager):
        reporter = EventReporter()
        # 模拟DroneInfoManager
        reporter.drone_manager = MagicMock()
        drone_info = MagicMock()
        drone_info.drone_code = "TEST_DRONE"
        drone_info.longitude = 120.0
        drone_info.latitude = 30.0
        reporter.drone_manager.get_drone_info.return_value = drone_info
        logger.info("event_reporter fixture 设置完成")
        yield reporter
        logger.info("清理 event_reporter fixture")


@pytest.mark.asyncio
async def test_save_detection_video(event_reporter):
    """测试保存检测视频功能"""
    logger.info("开始测试 save_detection_video")
    # 模拟StreamProcessor
    stream_processor = MagicMock()
    stream_processor.drone_code = "TEST_DRONE"
    stream_processor.frame_rate = 25
    
    # 创建模拟帧
    mock_frame = MagicMock()
    mock_frame.frame = pytest.importorskip("numpy").zeros((720, 1280, 3), dtype='uint8')
    stream_processor.get_frames_around_timestamp.return_value = [mock_frame] * 10
    
    # 调用被测试方法
    timestamp = 1609459200000  # 2021-01-01 00:00:00
    logger.info(f"调用 save_detection_video，时间戳: {timestamp}")
    video_path = await event_reporter.save_detection_video(stream_processor, timestamp)
    
    # 验证结果
    logger.info(f"save_detection_video 返回路径: {video_path}")
    assert video_path == "/path/to/test_video.mp4"
    event_reporter.file_manager.save_video.assert_called_once()
    args, kwargs = event_reporter.file_manager.save_video.call_args
    assert len(kwargs['frames']) == 10
    assert kwargs['frame_rate'] == 25
    logger.info("save_detection_video 测试完成")


@pytest.mark.asyncio
async def test_report_event_success(event_reporter):
    """测试成功上报事件"""
    logger.info("开始测试 report_event_success")
    # 模拟检测结果
    result = {
        "drone_code": "TEST_DRONE",
        "timestamp": 1609459200000,  # 2021-01-01 00:00:00
        "result": {
            "target_category": "船只",
            "detections": [
                {
                    "bbox": [100, 100, 200, 200],
                    "confidence": 0.95,
                    "class": "ship"
                }
            ],
            "error_code": "0",
            "image_paths": {
                "init": "/path/to/init_image.jpg",
                "res": "/path/to/res_image.jpg"
            }
        }
    }
    
    # 模拟StreamProcessor
    stream_processor = MagicMock()
    
    # 模拟save_detection_video方法，确保返回有效路径
    with patch.object(event_reporter, 'save_detection_video', new_callable=AsyncMock) as mock_save_video:
        mock_save_video.return_value = "/path/to/test_video.mp4"
        
        # 模拟HTTP响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value='{"code": 0, "message": "success"}')
        
        # 模拟aiohttp.ClientSession
        mock_session = AsyncMock()
        mock_session.__aenter__.return_value = mock_session
        mock_session.post.return_value.__aenter__.return_value = mock_response
        
        # 使用patch模拟aiohttp.ClientSession和os.path.exists
        with patch('aiohttp.ClientSession', return_value=mock_session), \
             patch('os.path.exists', return_value=True):
            # 调用被测试方法
            logger.info("调用 report_event 方法")
            await event_reporter.report_event(result, stream_processor)
            logger.info("report_event 方法调用完成")
        
        # 验证结果
        mock_save_video.assert_called_once_with(stream_processor, result["timestamp"])
        
        # 验证post被调用了两次
        assert mock_session.post.call_count == 2
        
        # 验证第一次调用是事件上报
        event_call = mock_session.post.call_args_list[0]
        assert event_call[0][0] == event_reporter.event_api_url
        assert event_call[1]["json"]["imageinit"] == "init_image.jpg"
        assert event_call[1]["json"]["snNum"] == "TEST_DRONE"
        assert event_call[1]["json"]["business_type"] == "船只"
        
        # 验证第二次调用是视频上传
        video_call = mock_session.post.call_args_list[1]
        assert video_call[0][0] == event_reporter.video_api_url
        assert video_call[1]["json"]["video"] == "test_video.mp4"
        assert video_call[1]["json"]["snNum"] == "TEST_DRONE"
        
        logger.info("report_event_success 测试完成")


@pytest.mark.asyncio
async def test_report_event_no_drone_info(event_reporter):
    """测试无人机信息不存在的情况"""
    logger.info("开始测试 report_event_no_drone_info")
    # 模拟检测结果
    result = {
        "drone_code": "UNKNOWN_DRONE",
        "timestamp": 1609459200000,
        "result": {"target_category": "船只"}
    }
    
    # 模拟StreamProcessor
    stream_processor = MagicMock()
    
    # 设置drone_manager返回None
    event_reporter.drone_manager.get_drone_info.return_value = None
    
    # 调用被测试方法
    logger.info("调用 report_event 方法，使用未知无人机编码")
    await event_reporter.report_event(result, stream_processor)
    logger.info("report_event 方法调用完成")
    
    # 验证结果 - 应该提前返回，不调用save_detection_video
    event_reporter.file_manager.save_video.assert_not_called()
    logger.info("report_event_no_drone_info 测试完成")


@pytest.mark.asyncio
async def test_report_video_info(event_reporter):
    """测试上报视频信息功能"""
    logger.info("开始测试 report_video_info")
    # 模拟HTTP响应
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.text = AsyncMock(return_value='{"code": 0, "message": "success"}')
    
    # 模拟aiohttp.ClientSession
    mock_session = AsyncMock()
    mock_session.__aenter__.return_value = mock_session
    mock_session.post.return_value.__aenter__.return_value = mock_response
    
    # 使用patch模拟aiohttp.ClientSession和os.path.exists
    with patch('aiohttp.ClientSession', return_value=mock_session), \
         patch('os.path.exists', return_value=True):
        # 调用被测试方法
        logger.info("调用 _report_video_info 方法")
        await event_reporter._report_video_info("test_video.mp4", "TEST_DRONE", 1609459200000)
        logger.info("_report_video_info 方法调用完成")
    
    # 验证结果
    mock_session.post.assert_called_once()
    args, kwargs = mock_session.post.call_args
    assert args[0] == event_reporter.video_api_url
    assert kwargs["json"]["video"] == "test_video.mp4"
    assert kwargs["json"]["snNum"] == "TEST_DRONE"
    logger.info("report_video_info 测试完成") 