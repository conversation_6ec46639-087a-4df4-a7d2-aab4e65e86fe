#!/usr/bin/env python3
"""
测试共享状态管理器功能（不依赖数据库）
"""

import sys
import os
import time
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_shared_state_manager():
    """测试共享状态管理器"""
    logger.info("=== 测试共享状态管理器 ===")
    
    try:
        from app.utils.config import config
        from app.utils.shared_state import shared_state_manager, active_drones
        
        # 清空现有状态
        active_drones.clear()
        
        # 显示配置信息
        logger.info(f"配置的无人机列表: {config.DRONE_LIST}")
        logger.info(f"只监控配置无人机: {config.stream_monitor.only_monitor_config_drones}")
        logger.info(f"活跃模式代码: {config.WEBSOCKET_ACTIVE_MODE_CODES}")
        
        # 获取配置中的无人机代码
        config_drone_codes = config.get_drone_codes()

        # 模拟添加一些无人机状态（包括配置中的和不在配置中的）
        test_drones = [
            {
                'device_sn': 'drone001',  # 不在配置中
                'mode_code': 4,  # 活跃模式
                'is_active': True,
                'last_update': time.time()
            },
            {
                'device_sn': 'drone002',  # 不在配置中
                'mode_code': 1,  # 非活跃模式
                'is_active': False,
                'last_update': time.time()
            },
            {
                'device_sn': 'drone003',  # 不在配置中
                'mode_code': 3,  # 活跃模式
                'is_active': True,
                'last_update': time.time()
            }
        ]

        # 如果配置中有无人机，也添加一个配置中的活跃无人机
        if config_drone_codes:
            test_drones.append({
                'device_sn': config_drone_codes[0],  # 配置中的第一个无人机
                'mode_code': 4,  # 活跃模式
                'is_active': True,
                'last_update': time.time()
            })
        
        # 添加测试数据
        for drone_status in test_drones:
            device_sn = drone_status['device_sn']
            shared_state_manager.update_drone_status(device_sn, drone_status)
            logger.info(f"添加测试无人机: {device_sn}")
        
        # 检查原始数据
        logger.info(f"\n原始 active_drones 数据: {dict(active_drones)}")
        
        # 测试获取活跃无人机（简化版本，只检查配置列表）
        logger.info("\n--- 测试简化的活跃无人机检测 ---")
        active_drones_result = shared_state_manager.get_active_drones()
        active_ids_result = shared_state_manager.get_active_drone_ids()
        logger.info(f"活跃无人机: {list(active_drones_result.keys())}")
        logger.info(f"活跃无人机ID: {active_ids_result}")
        
        # 测试活跃状态统计
        logger.info("\n--- 测试活跃状态统计 ---")
        summary = shared_state_manager.get_activity_summary()
        logger.info(f"活跃状态统计: {summary}")
        
        # 测试单个无人机活跃状态检查
        logger.info("\n--- 测试单个无人机活跃状态检查 ---")
        for drone_id in ['drone001', 'drone002', 'drone003', 'nonexistent']:
            is_active = shared_state_manager.is_drone_active(drone_id)
            logger.info(f"无人机 {drone_id} 是否活跃: {is_active}")
        
        # 验证结果（简化版本：只检查配置中的活跃无人机）
        expected_active = []
        if config_drone_codes:
            expected_active = [config_drone_codes[0]]  # 只有配置中的活跃无人机

        actual_active = active_ids_result

        logger.info(f"\n预期活跃无人机: {expected_active}")
        logger.info(f"实际活跃无人机: {actual_active}")

        # 检查结果是否正确
        success = set(expected_active) == set(actual_active)

        if success:
            logger.info("✅ 简化活跃无人机检测测试通过")
        else:
            logger.error("❌ 简化活跃无人机检测测试失败")
        
        if success:
            logger.info("✅ 共享状态管理器测试通过！")
        else:
            logger.error("❌ 共享状态管理器测试失败！")
        
        return success
        
    except Exception as e:
        logger.error(f"测试共享状态管理器失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试共享状态管理器功能")
    
    success = test_shared_state_manager()
    
    if success:
        logger.info("\n🎉 测试通过！共享状态管理器功能正常。")
    else:
        logger.error("\n❌ 测试失败，需要进一步检查。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
