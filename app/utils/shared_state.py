"""
简化的共享状态模块
只负责活跃无人机检测
"""
import time
import logging
from typing import Dict, Any, List
from threading import Lock

logger = logging.getLogger(__name__)

class SharedStateManager:
    """简化的共享状态管理器"""

    def __init__(self):
        """初始化共享状态管理器"""
        self._lock = Lock()

    def get_active_drones(self) -> Dict[str, Dict[str, Any]]:
        """
        获取活跃无人机状态
        逻辑：遍历配置列表中的无人机，检查每个是否有最近的有效WebSocket数据
        :return: 活跃无人机状态字典
        """
        with self._lock:
            try:
                from app.utils.config import config
                current_time = time.time()

                # 获取配置的无人机代码列表
                config_drone_codes = config.get_drone_codes()
                logger.debug(f"检查配置列表中的无人机: {config_drone_codes}")

                active_result = {}

                # 遍历配置的无人机，检查是否活跃
                for drone_code in config_drone_codes:
                    # 检查是否在 active_drones 中（说明有WebSocket数据）
                    if drone_code not in active_drones:
                        logger.debug(f"❌ 无人机 {drone_code} 无WebSocket数据")
                        continue

                    status = active_drones[drone_code]

                    # 检查数据时效性（5分钟超时）
                    last_update = status.get('last_update', 0)
                    if current_time - last_update > 300:
                        logger.debug(f"❌ 无人机 {drone_code} 数据过期（{current_time - last_update:.1f}秒前）")
                        continue

                    # 检查是否标记为活跃
                    if not status.get('is_active', False):
                        logger.debug(f"❌ 无人机 {drone_code} 未标记为活跃")
                        continue

                    # 检查模式代码
                    mode_code = status.get('mode_code')
                    if mode_code is None or mode_code not in config.WEBSOCKET_ACTIVE_MODE_CODES:
                        logger.debug(f"❌ 无人机 {drone_code} 模式代码无效: {mode_code}")
                        continue

                    # 通过所有检查
                    active_result[drone_code] = status
                    logger.info(f"✅ 无人机 {drone_code} 活跃 (mode: {mode_code}, 更新: {current_time - last_update:.1f}秒前)")

                logger.info(f"活跃无人机检查完成: {len(active_result)}/{len(config_drone_codes)} 个活跃")
                return active_result

            except Exception as e:
                logger.error(f"获取活跃无人机失败: {str(e)}")
                return {}
    
    def get_active_drone_ids(self) -> List[str]:
        """获取活跃无人机ID列表"""
        active_data = self.get_active_drones()
        return list(active_data.keys())

    def is_drone_active(self, drone_code: str) -> bool:
        """检查指定无人机是否活跃"""
        active_drones_dict = self.get_active_drones()
        return drone_code in active_drones_dict

    def update_drone_status(self, drone_code: str, status: Dict[str, Any]):
        """更新无人机状态（线程安全）"""
        with self._lock:
            active_drones[drone_code] = status
            logger.debug(f"更新无人机状态: {drone_code}")

    def remove_drone_status(self, drone_code: str):
        """移除无人机状态（线程安全）"""
        with self._lock:
            if drone_code in active_drones:
                del active_drones[drone_code]
                logger.debug(f"移除无人机状态: {drone_code}")


# 全局变量，用于存储无人机状态信息
# 键为无人机ID (device_sn)，值为包含状态信息的字典
# 例如: {
#   'device_sn': 'xxx', 
#   'mode_code': 4, 
#   'is_active': True, 
#   'last_update': timestamp,
#   'longitude': 120.123456,  # 经度
#   'latitude': 30.123456     # 纬度
# }
active_drones: Dict[str, Dict[str, Any]] = {}

# 创建共享状态管理器实例
shared_state_manager = SharedStateManager()

# 如果将来需要更多共享状态，可以在这里添加 