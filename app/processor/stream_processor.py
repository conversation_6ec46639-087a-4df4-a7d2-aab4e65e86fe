import asyncio
import cv2
import logging
import threading
import time
from typing import Optional, List, Dict, Callable, Any
import numpy as np
from dataclasses import dataclass
from app.processor.image_processor import ImagePreprocessor
from app.utils.config import config
import uuid
from app.utils.file_manager import file_manager
from app.video.manager import VideoStreamManager
import os
import concurrent.futures
from app.database.websocket_client import WebSocketClient
from app.utils.frame_buffer import FrameBuffer, FrameInfo

logger = logging.getLogger(__name__)

class StreamProcessor:
    """视频流处理器，负责处理单个视频流的检测"""
    def __init__(self, drone_code: str, stream_id: str, stream_type: str = "drone", model_type: str = 'cv', 
                 on_detection: Optional[Callable[[Dict], None]] = None):
        """
        初始化流处理器
        :param drone_code: 无人机编号
        :param stream_id: 视频流ID
        :param stream_type: 视频流类型 ('drone' 或 'airport')
        :param model_type: 模型类型 ('cv' 或 'multimodal')
        :param on_detection: 检测到目标时的回调函数
        """
        self.drone_code = drone_code
        self.stream_id = stream_id
        self.stream_type = stream_type
        self.stream_key = f"{drone_code}_{stream_type}"
        self.logger = logger
        self.image_processor = ImagePreprocessor(model_type=model_type)
        self.frame_rate = config.video_stream.frame_rate  # 默认帧率
        self.is_running = False
        self.process_thread: Optional[threading.Thread] = None
        self.last_frame_time = 0
        self.loop = asyncio.get_event_loop()
        self.read_fail_threshold = config.video_stream.read_fail_threshold
        self.detection_interval = config.video_stream.detection_interval
        self.on_detection = on_detection
        
        # 初始化帧缓存
        self.frame_buffer = FrameBuffer(config.video_stream.buffer_duration)
        # 添加视频计数器
        self.video_counter = 0
        # 视频流对象
        self.stream = None
        # 最后一次成功读取帧的时间
        self.last_successful_read = 0

    def get_frames_around_timestamp(self, timestamp: float, duration: float) -> List[FrameInfo]:
        """
        获取指定时间戳前后指定时长的帧
        :param timestamp: 目标时间戳（毫秒）
        :param duration: 前后时长（秒）
        :return: 帧列表
        """
        return self.frame_buffer.get_frames_around_timestamp(timestamp, duration)

    def get_frames_before_timestamp(self, timestamp: float, duration: float) -> List[FrameInfo]:
        """
        获取指定时间戳前面指定时长的帧
        :param timestamp: 目标时间戳（毫秒）
        :param duration: 前面的时长（秒）
        :return: 帧列表
        """
        return self.frame_buffer.get_frames_before_timestamp(timestamp, duration)

    async def start(self, stream) -> bool:
        """
        启动流处理
        :param stream: 视频流对象
        :return: 是否成功启动
        """
        if self.is_running:
            self.logger.warning(f"流处理器已经在运行: {self.stream_key}")
            return False
            
        if not stream:
            self.logger.error(f"无效的视频流对象: {self.stream_key}")
            return False
            
        # 不再检查流是否活跃，直接启动处理器
        self.stream = stream
        self.is_running = True
        self.process_thread = threading.Thread(
            target=self._process_stream,
            name=f"StreamProcessor-{self.stream_key}"
        )
        self.process_thread.daemon = True
        self.process_thread.start()
        self.logger.info(f"启动流处理器: {self.stream_key}")
        return True
        
    async def stop(self):
        """停止处理流"""
        if not self.is_running:
            return
            
        self.logger.info(f"停止流处理器: {self.stream_key}")
        self.is_running = False
        if self.process_thread:
            self.process_thread.join(timeout=5.0)
            if self.process_thread.is_alive():
                self.logger.warning(f"流处理线程未能正常结束: {self.stream_key}")
        self.stream = None
            
    def _process_stream(self):
        """处理流的主循环 - 重构为从流队列获取帧"""
        last_detection_time = 0
        read_fail_count = 0
        
        self.logger.info(f"开始处理流循环: {self.stream_key}")
        
        while self.is_running:
            try:
                # 从流获取帧 - 新架构下这个方法已经从队列获取帧
                frame_future = asyncio.run_coroutine_threadsafe(
                    self.stream.get_frame(), self.loop
                )
                
                try:
                    frame = frame_future.result(timeout=config.FRAME_QUEUE_TIMEOUT)
                except concurrent.futures.TimeoutError:
                    read_fail_count += 1
                    
                    # 定期检查流状态
                    if read_fail_count % 10 == 0:
                        self.logger.debug(f"获取帧超时，检查流状态: {self.stream_key}, 超时次数: {read_fail_count}")
                        
                        try:
                            # 检查流是否仍然活跃
                            check_future = asyncio.run_coroutine_threadsafe(
                                self.stream.check_stream(), self.loop
                            )
                            
                            is_active = check_future.result(timeout=3.0)
                            
                            if not is_active:
                                self.logger.error(f"流不再活跃，停止处理: {self.stream_key}")
                                asyncio.run_coroutine_threadsafe(self.stop(), self.loop)
                                break
                        except Exception as e:
                            self.logger.error(f"检查流状态时出错: {self.stream_key}, 错误: {str(e)}")
                    
                    # 如果连续超时次数过多，停止处理
                    if read_fail_count >= self.read_fail_threshold:
                        self.logger.error(f"连续获取帧失败次数超过{self.read_fail_threshold}，停止处理: {self.stream_key}")
                        asyncio.run_coroutine_threadsafe(self.stop(), self.loop)
                        break
                        
                    # 短暂等待后继续尝试
                    time.sleep(0.1)
                    continue
                
                if frame is None:
                    read_fail_count += 1
                    if read_fail_count >= self.read_fail_threshold:
                        self.logger.error(f"连续获取空帧次数超过{self.read_fail_threshold}，停止处理: {self.stream_key}")
                        asyncio.run_coroutine_threadsafe(self.stop(), self.loop)
                        break
                    time.sleep(0.1)
                    continue

                # 重置失败计数
                read_fail_count = 0
                current_time = time.time()
                self.last_successful_read = current_time
                timestamp = int(current_time * 1000)  # 转换为毫秒

                # 添加帧到缓存
                try:
                    self.frame_buffer.add_frame(frame, timestamp)
                except Exception as e:
                    self.logger.error(f"添加帧到缓存失败: {self.stream_key}, 错误: {str(e)}")
                    # 继续处理，不因为缓存问题停止
                
                # 每100帧记录一次缓存状态
                if self.frame_buffer.frame_count % 100 == 0:
                    self.logger.debug(f"帧缓存状态: 总帧数={self.frame_buffer.frame_count}, 缓存帧数={len(self.frame_buffer.frames)}")

                # 按照检测间隔进行目标检测
                if current_time - last_detection_time >= self.detection_interval:
                    try:
                        # 获取所有目标配置
                        target_configs = [
                            next((t for t in config.detection.targets if t['id'] == int(target_id)), None)
                            for target_id in config.detection.target_ids
                        ]
                        target_configs = [t for t in target_configs if t is not None]
                        
                        # 创建检测任务并立即返回，不等待结果
                        detection_coroutine = self.image_processor.detect_targets(frame.copy(), target_configs)
                        
                        # 使用asyncio.create_task创建任务
                        detection_task = asyncio.run_coroutine_threadsafe(
                            detection_coroutine, 
                            self.loop
                        )
                        
                        # 添加回调函数，当任务完成时处理结果
                        detection_task.add_done_callback(
                            lambda t: asyncio.run_coroutine_threadsafe(
                                self._handle_detection_result(t, timestamp),
                                self.loop
                            )
                        )
                        
                    except Exception as e:
                        self.logger.error(f"创建目标检测任务时出错: {self.stream_key}, 错误: {str(e)}")
                    
                    # 更新最后检测时间
                    last_detection_time = current_time
                    
                # 控制处理速度
                time.sleep(1.0 / self.frame_rate)
                    
            except Exception as e:
                self.logger.error(f"处理流时出错: {self.stream_key}, 错误: {str(e)}")
                import traceback
                self.logger.error(f"错误详情: {traceback.format_exc()}")
                # 短暂等待后继续尝试
                time.sleep(1)
        
        self.logger.info(f"流处理循环结束: {self.stream_key}")

    async def _handle_detection_result(self, task, timestamp):
        """
        处理检测任务的结果
        :param task: 检测任务
        :param timestamp: 检测时间戳
        """
        try:
            # 获取任务结果
            result = task.result()
            
            # 如果检测到目标，处理结果
            if result:
                # self.logger.info(f"检测到目标: {self.stream_key}, 结果类型: {type(result)}")
                
                # 在结果中添加必要的信息
                result_with_metadata = {
                    "timestamp": timestamp,
                    "drone_code": self.drone_code,
                    "stream_type": self.stream_type,
                    "result": result  # 这里的result已经是完成的结果，不是协程
                }
                
                # 如果有回调函数，调用它
                if self.on_detection:
                    await self._call_detection_callback(result_with_metadata)
                    
        except asyncio.CancelledError:
            self.logger.warning(f"检测任务被取消: {self.stream_key}")
        except Exception as e:
            self.logger.error(f"处理检测结果时出错: {self.stream_key}, 错误: {str(e)}")
            import traceback
            self.logger.error(f"处理检测结果错误详情: {traceback.format_exc()}")

    async def _call_detection_callback(self, result: Dict):
        """调用检测回调函数"""
        try:
            # 调用回调函数
            if self.on_detection:
                self.logger.info(f"准备上报事件: {self.stream_key}")
                await self.on_detection(result, self)
        except Exception as e:
            self.logger.error(f"调用检测回调函数失败: {str(e)}")
            import traceback
            self.logger.error(f"上报事件异常详情: {traceback.format_exc()}")

class StreamMonitor:
    """流监控器 - 管理多个视频流处理器"""
    
    def __init__(self, video_manager: VideoStreamManager):
        """
        初始化监控器
        :param video_manager: 视频流管理器实例
        """
        self.video_manager = video_manager
        self.check_interval = config.stream_monitor.check_interval
        self.is_running = False
        self._monitor_task = None
        # 存储所有活跃的处理器，键为 "{drone_code}_{stream_type}"
        self.processors: Dict[str, StreamProcessor] = {}
        
    async def start(self):
        """启动监控"""
        if self.is_running:
            logger.warning("监控器已经在运行")
            return
            
        self.is_running = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("流监控器已启动")
        
    async def stop(self):
        """停止监控"""
        if not self.is_running:
            return
            
        self.is_running = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
                
        # 停止所有处理器
        stop_tasks = []
        for processor in self.processors.values():
            stop_tasks.append(processor.stop())
        if stop_tasks:
            await asyncio.gather(*stop_tasks)
            
        self.processors.clear()
        await self.video_manager.cleanup()
        logger.info("流监控器已停止")
        
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 使用统一的共享状态管理器获取活跃无人机列表
                # 这里已经包含了配置过滤逻辑，无需重复过滤
                from app.utils.shared_state import shared_state_manager
                active_drone_ids = shared_state_manager.get_active_drone_ids(apply_config_filter=True)

                logger.debug(f"流监控器获取到活跃无人机列表: {active_drone_ids}")

                # 处理每个活跃的无人机
                for drone_code in active_drone_ids:
                    await self._manage_drone_streams(drone_code)

                # 清理不再活跃的处理器
                await self._cleanup_inactive_processors(active_drone_ids)
                    
            except Exception as e:
                logger.error(f"监控循环发生错误: {str(e)}")
                import traceback
                logger.error(f"错误详情: {traceback.format_exc()}")
                
            await asyncio.sleep(self.check_interval)
            
    async def _manage_drone_streams(self, drone_code: str):
        """
        管理单个无人机的视频流
        :param drone_code: 无人机编号
        """
        # 处理无人机视频流
        await self._manage_stream(drone_code, "drone")
        
        # 处理机场视频流（如果配置了）
        if config.video_stream.process_airport_stream:
            await self._manage_stream(drone_code, "airport")
            
    async def _manage_stream(self, drone_code: str, stream_type: str):
        """
        管理特定无人机的特定类型视频流
        :param drone_code: 无人机编号
        :param stream_type: 视频流类型
        """
        # 构建流标识符
        stream_key = f"{drone_code}_{stream_type}"
        logger.debug(f"管理视频流: {stream_key}")
        
        try:
            # 获取视频流
            stream = await self.video_manager.get_stream(drone_code, stream_type)
            
            # 如果无法获取流，记录日志并返回
            if not stream:
                logger.warning(f"无法获取视频流: {stream_key}")
                if stream_key in self.processors:
                    logger.info(f"视频流不可用，停止处理器: {stream_key}")
                    await self.processors[stream_key].stop()
                    del self.processors[stream_key]
                return
                
            # 不再检查流是否活跃，直接创建处理器
            # 如果处理器不存在或不在运行，创建并启动新的处理器
            if stream_key not in self.processors or not self.processors[stream_key].is_running:
                logger.info(f"创建新的流处理器: {stream_key}")
                processor = StreamProcessor(
                    drone_code=drone_code,
                    stream_id=stream_key,
                    stream_type=stream_type,
                    on_detection=self._on_detection
                )
                
                # 启动处理器
                if await processor.start(stream):
                    self.processors[stream_key] = processor
                    logger.info(f"成功启动流处理器: {stream_key}")
                else:
                    logger.error(f"启动流处理器失败: {stream_key}")
                    
        except Exception as e:
            logger.error(f"管理视频流失败: {stream_key}, 错误: {str(e)}")
            import traceback
            logger.error(f"管理视频流错误详情: {traceback.format_exc()}")
            
    async def _cleanup_inactive_processors(self, active_drone_ids: List[str]):
        """
        清理不再活跃的处理器
        :param active_drone_ids: 活跃的无人机列表
        """
        # 找出不再活跃的处理器
        inactive_keys = []
        for stream_key, processor in self.processors.items():
            # 检查处理器对应的无人机是否仍然活跃
            drone_code = processor.drone_code
            if drone_code not in active_drone_ids:
                logger.info(f"无人机 {drone_code} 不再活跃，标记处理器 {stream_key} 为不活跃")
                inactive_keys.append(stream_key)
                continue
                
            # 检查处理器是否长时间未收到帧
            if processor.last_successful_read > 0:
                inactive_time = time.time() - processor.last_successful_read
                if inactive_time > config.stream_monitor.check_interval * 2:
                    logger.warning(f"处理器长时间未收到帧: {stream_key}, {inactive_time:.1f}秒")
                    inactive_keys.append(stream_key)
        
        # 停止不再活跃的处理器
        for stream_key in inactive_keys:
            if stream_key in self.processors:
                logger.info(f"停止不再活跃的处理器: {stream_key}")
                await self.processors[stream_key].stop()
                del self.processors[stream_key]
                
    async def _on_detection(self, result: Dict, processor: StreamProcessor):
        """
        检测到目标时的回调函数
        :param result: 检测结果
        :param processor: 处理器实例
        """
        try:
            logger.info(f"检测到目标: {processor.stream_key}, 结果类型: {type(result['result'])}, 内容: {result['result'].get('detections')}")
            
            # 保存视频片段
            timestamp = result["timestamp"]
            logger.info(f"准备保存检测视频，时间戳: {timestamp}")
            
            try:
                # 使用FileManager的save_detection_video方法保存视频
                video_path = await file_manager.save_detection_video(processor, timestamp)
                
                if not video_path:
                    logger.error(f"保存视频失败: {processor.stream_key}, 时间戳: {timestamp}")
                else:
                    # 添加视频路径到结果中
                    result["video_path"] = video_path
                    logger.info(f"成功保存视频: {video_path}, 文件大小: {os.path.getsize(video_path) if os.path.exists(video_path) else '文件不存在'} 字节")
            except Exception as e:
                logger.error(f"保存视频过程中发生异常: {str(e)}")
                import traceback
                logger.error(f"保存视频异常详情: {traceback.format_exc()}")
            
            # 上报事件
            try:
                logger.info(f"准备上报事件: {processor.stream_key}")
                from app.utils.event_reporter import event_reporter
                report_result = await event_reporter.report_event(result, processor)
                if report_result:
                    logger.info(f"成功上报事件: {processor.stream_key}")
                else:
                    logger.warning(f"事件上报可能未成功: {processor.stream_key}")
            except Exception as e:
                logger.error(f"上报事件失败: {str(e)}")
                import traceback
                logger.error(f"上报事件异常详情: {traceback.format_exc()}")
            
        except Exception as e:
            logger.error(f"处理检测结果失败: {str(e)}")
            import traceback
            logger.error(f"处理检测结果异常详情: {traceback.format_exc()}")