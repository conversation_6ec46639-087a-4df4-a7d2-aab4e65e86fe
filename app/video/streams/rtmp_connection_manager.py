import asyncio
import cv2
import logging
import threading
import time
from typing import Optional
from app.utils.config import config
import os

logger = logging.getLogger(__name__)

# 设置OpenCV FFMPEG读取尝试次数环境变量
if 'OPENCV_FFMPEG_READ_ATTEMPTS' not in os.environ:
    os.environ['OPENCV_FFMPEG_READ_ATTEMPTS'] = '20000'
    logger.info(f"设置OPENCV_FFMPEG_READ_ATTEMPTS环境变量为: {os.environ['OPENCV_FFMPEG_READ_ATTEMPTS']}")

class RTMPConnectionManager:
    """RTMP连接管理器，负责连接的生命周期管理"""
    
    def __init__(self, rtmp_url: str):
        """
        初始化连接管理器
        :param rtmp_url: RTMP流URL
        """
        self.rtmp_url = rtmp_url
        self._cap: Optional[cv2.VideoCapture] = None
        self._connection_lock = threading.Lock()
        self._is_connected = False
        self._last_health_check = 0.0
        self._consecutive_failures = 0
        self._max_failures = config.CONNECTION_RECONNECT_MAX_ATTEMPTS
        self._reconnect_delay = config.CONNECTION_RECONNECT_DELAY
        
        # 连接参数
        self._open_timeout = config.CONNECTION_OPEN_TIMEOUT
        self._read_timeout = config.CONNECTION_READ_TIMEOUT
        self._buffer_size = config.CONNECTION_BUFFER_SIZE
        
    async def create_connection(self) -> bool:
        """
        创建RTMP连接
        :return: 是否成功创建连接
        """
        try:
            with self._connection_lock:
                # 释放旧连接
                if self._cap:
                    try:
                        self._cap.release()
                    except Exception as e:
                        logger.warning(f"释放旧连接时出错: {str(e)}")
                    finally:
                        self._cap = None
                
                logger.info(f"创建RTMP连接: {self.rtmp_url}")
                
                # 创建新的VideoCapture，添加异常保护
                try:
                    self._cap = cv2.VideoCapture(self.rtmp_url, cv2.CAP_FFMPEG)
                except Exception as e:
                    logger.error(f"创建VideoCapture失败: {self.rtmp_url}, 错误: {str(e)}")
                    self._cap = None
                    self._is_connected = False
                    return False
                
                # 设置连接参数
                try:
                    self._cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, self._open_timeout)
                    self._cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, self._read_timeout)
                    self._cap.set(cv2.CAP_PROP_BUFFERSIZE, self._buffer_size)
                except Exception as e:
                    logger.warning(f"设置连接参数时出错: {str(e)}")
                
                # 检查连接是否成功
                try:
                    if not self._cap.isOpened():
                        logger.error(f"无法打开RTMP连接: {self.rtmp_url}")
                        self._cap = None
                        self._is_connected = False
                        return False
                except Exception as e:
                    logger.error(f"检查连接状态时出错: {self.rtmp_url}, 错误: {str(e)}")
                    if self._cap:
                        try:
                            self._cap.release()
                        except:
                            pass
                        self._cap = None
                    self._is_connected = False
                    return False
                
                # 尝试读取一帧验证连接，添加超时保护
                try:
                    ret, frame = self._cap.read()
                    if not ret or frame is None or frame.size == 0:
                        logger.error(f"RTMP连接无法读取有效帧: {self.rtmp_url}")
                        self._cap.release()
                        self._cap = None
                        self._is_connected = False
                        return False
                except Exception as e:
                    logger.error(f"读取验证帧时出错: {self.rtmp_url}, 错误: {str(e)}")
                    if self._cap:
                        try:
                            self._cap.release()
                        except:
                            pass
                        self._cap = None
                    self._is_connected = False
                    return False
                
                self._is_connected = True
                self._consecutive_failures = 0
                self._last_health_check = time.time()
                logger.info(f"成功创建RTMP连接: {self.rtmp_url}")
                return True
                
        except Exception as e:
            logger.error(f"创建RTMP连接时发生严重错误: {self.rtmp_url}, 错误: {str(e)}")
            with self._connection_lock:
                if self._cap:
                    try:
                        self._cap.release()
                    except:
                        pass
                    self._cap = None
                self._is_connected = False
            return False
    
    async def check_health(self) -> bool:
        """
        检查连接健康状态
        :return: 连接是否健康
        """
        current_time = time.time()
        
        # 如果距离上次检查时间太短，返回缓存结果
        if current_time - self._last_health_check < 5.0:
            return self._is_connected
        
        try:
            with self._connection_lock:
                # 如果没有连接，直接返回False
                if not self._cap or not self._cap.isOpened():
                    self._is_connected = False
                    return False
                
                # 尝试读取一帧检查连接
                start_time = time.time()
                ret, frame = self._cap.read()
                read_time = time.time() - start_time
                
                if ret and frame is not None and frame.size > 0:
                    self._is_connected = True
                    self._consecutive_failures = 0
                    self._last_health_check = current_time
                    logger.debug(f"RTMP连接健康检查通过: {self.rtmp_url}, 耗时: {read_time:.2f}秒")
                    return True
                else:
                    self._consecutive_failures += 1
                    self._is_connected = False
                    logger.warning(f"RTMP连接健康检查失败: {self.rtmp_url}, 连续失败: {self._consecutive_failures}")
                    return False
                    
        except Exception as e:
            self._consecutive_failures += 1
            self._is_connected = False
            logger.error(f"RTMP连接健康检查出错: {self.rtmp_url}, 错误: {str(e)}")
            return False
    
    async def reconnect(self) -> bool:
        """
        重新连接
        :return: 是否成功重连
        """
        logger.info(f"尝试重新连接RTMP: {self.rtmp_url}")
        
        # 等待一段时间后重连
        await asyncio.sleep(self._reconnect_delay)
        
        # 尝试创建新连接
        success = await self.create_connection()
        
        if success:
            logger.info(f"成功重新连接RTMP: {self.rtmp_url}")
        else:
            logger.error(f"重新连接RTMP失败: {self.rtmp_url}")
            
        return success
    
    async def close_connection(self) -> None:
        """关闭连接"""
        logger.info(f"关闭RTMP连接: {self.rtmp_url}")
        
        with self._connection_lock:
            if self._cap:
                self._cap.release()
                self._cap = None
            self._is_connected = False
    
    def get_video_capture(self) -> Optional[cv2.VideoCapture]:
        """
        获取VideoCapture对象（仅供生产者使用）
        :return: VideoCapture对象或None
        """
        with self._connection_lock:
            if self._is_connected and self._cap and self._cap.isOpened():
                return self._cap
            return None
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._is_connected
    
    def get_consecutive_failures(self) -> int:
        """获取连续失败次数"""
        return self._consecutive_failures
    
    def should_reconnect(self) -> bool:
        """判断是否应该重连"""
        return (not self._is_connected and 
                self._consecutive_failures < self._max_failures)
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        return {
            "url": self.rtmp_url,
            "is_connected": self._is_connected,
            "consecutive_failures": self._consecutive_failures,
            "last_health_check": self._last_health_check,
            "max_failures": self._max_failures
        } 