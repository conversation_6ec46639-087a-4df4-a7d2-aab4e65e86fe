import asyncio
import aiohttp
import logging
import json
import time
import random
from datetime import datetime
from typing import Optional, Dict, Any, Callable
from app.utils.config import config
from app.utils.shared_state import active_drones
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

logger = logging.getLogger(__name__)

class WebSocketClient:
    """WebSocket客户端，用于处理无人机数据流"""
    
    def __init__(self):
        """初始化WebSocket客户端"""
        self.token: Optional[str] = None
        self.workspace_id: Optional[str] = None
        self.ws: Optional[aiohttp.ClientWebSocketResponse] = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.message_handlers: Dict[str, Callable] = {}
        self._running = False
        self.scheduler = AsyncIOScheduler()
        
        # 重连相关状态 - 使用配置参数
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = config.WEBSOCKET_MAX_RECONNECT_ATTEMPTS
        self.base_reconnect_delay = config.WEBSOCKET_BASE_RECONNECT_DELAY
        self.max_reconnect_delay = config.WEBSOCKET_MAX_RECONNECT_DELAY
        self.last_reconnect_time = 0
        self.last_message_time = time.time()
        self.connection_healthy = True
        
        # 智能休眠模式 - 使用配置参数
        self.night_mode_enabled = False
        self.night_mode_start_hour = config.WEBSOCKET_NIGHT_MODE_START_HOUR
        self.night_mode_end_hour = config.WEBSOCKET_NIGHT_MODE_END_HOUR
        self.night_mode_reconnect_interval = config.WEBSOCKET_NIGHT_MODE_RECONNECT_INTERVAL
        
        # 连接健康检查 - 使用配置参数
        self.health_check_interval = config.WEBSOCKET_HEALTH_CHECK_INTERVAL
        self.last_health_check = time.time()
        self.consecutive_failures = 0
        self.max_consecutive_failures = config.WEBSOCKET_MAX_CONSECUTIVE_FAILURES
        
        # 添加默认的无人机状态处理器
        self.register_message_handler('drone_status', self._process_drone_status)

    def _is_night_mode(self) -> bool:
        """判断是否处于夜间模式"""
        current_hour = datetime.now().hour
        return current_hour >= self.night_mode_start_hour or current_hour < self.night_mode_end_hour

    def _calculate_reconnect_delay(self) -> float:
        """计算重连延迟时间（指数退避）"""
        if self.reconnect_attempts == 0:
            return self.base_reconnect_delay
        
        # 指数退避算法
        delay = min(
            self.base_reconnect_delay * (2 ** (self.reconnect_attempts - 1)),  # 修正指数计算
            self.max_reconnect_delay
        )
        
        # 添加随机抖动，避免多个客户端同时重连
        jitter = random.uniform(0.8, 1.2)
        return delay * jitter

    def _should_attempt_reconnect(self) -> bool:
        """判断是否应该尝试重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.warning(f"已达到最大重连次数 {self.max_reconnect_attempts}，停止重连")
            return False
        
        # 检查重连间隔
        current_time = time.time()
        if current_time - self.last_reconnect_time < self._calculate_reconnect_delay():
            return False
        
        return True

    def _reset_reconnect_state(self):
        """重置重连状态"""
        self.reconnect_attempts = 0
        self.consecutive_failures = 0
        self.connection_healthy = True
        logger.info("重置WebSocket重连状态")

    async def _health_check(self) -> bool:
        """执行连接健康检查"""
        try:
            if not self.ws or self.ws.closed:
                return False
            
            # 检查最后消息时间
            current_time = time.time()
            if current_time - self.last_message_time > self.health_check_interval * 2:
                logger.warning("WebSocket连接长时间无消息，可能已断开")
                return False
            
            # 发送ping消息进行健康检查
            await self._send_pong()
            return True
            
        except Exception as e:
            logger.error(f"连接健康检查失败: {str(e)}")
            return False

    async def get_token(self) -> bool:
        """
        获取认证token
        :return: 是否成功获取token
        """
        try:
            base_url = config.WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/manage/api/v1/login",
                    json={
                        "username": config.WEBSOCKET_USERNAME,
                        "password": config.WEBSOCKET_PASSWORD,
                        "flag": config.WEBSOCKET_FLAG
                    }
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'data' in data:
                            self.token = data['data']['access_token']
                            self.workspace_id = data['data']['workspace_id']
                            logger.info("成功获取新token")
                            return True
                    logger.error(f"获取token失败: {response.status} - {await response.text()}")
                    return False
        except Exception as e:
            logger.error(f"获取token异常: {str(e)}")
            return False

    def get_websocket_url(self) -> str:
        """
        获取WebSocket URL
        :return: 完整的WebSocket URL
        """
        if not self.token:
            raise ValueError("Token未初始化")
            
        # 从 base_url 中提取主机和端口
        base_url = config.WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
        # 替换 http:// 为 ws://
        ws_url = base_url.replace('http://', 'ws://')
        # 添加 WebSocket 路径和 token
        return f"{ws_url}/api/v1/ws?x-auth-token={self.token}"

    async def validate_token(self) -> bool:
        """
        验证当前token是否有效
        :return: token是否有效
        """
        if not self.token:
            return False
            
        try:
            base_url = config.WEBSOCKET_URL.rstrip('/')  # 移除末尾的斜杠
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{base_url}/api/v1/validate_token",
                    headers={
                        "Authorization": f"Bearer {self.token}",
                        "workspace-id": self.workspace_id
                    }
                ) as response:
                    if response.status == 200:
                        return True
                    elif response.status == 401:
                        logger.warning("Token已过期，需要重新获取")
                        self.token = None
                        return False
                    else:
                        logger.error(f"验证token失败: {response.status} - {await response.text()}")
                        return False
        except Exception as e:
            logger.error(f"验证token时发生异常: {str(e)}")
            return False

    async def connect(self) -> bool:
        """
        建立WebSocket连接
        :return: 是否成功建立连接
        """
        try:
            # 如果没有token或token无效，重新获取
            if not self.token or not await self.validate_token():
                if not await self.get_token():
                    return False

            # 获取WebSocket URL
            ws_url = self.get_websocket_url()
            
            # 创建新的会话
            if self.session:
                await self.session.close()
            self.session = aiohttp.ClientSession()
            
            # 建立WebSocket连接，使用token进行认证
            self.ws = await self.session.ws_connect(
                ws_url,
                headers={
                    "Authorization": f"Bearer {self.token}",
                    "workspace-id": self.workspace_id
                }
            )
            
            # 连接成功后重置重连状态
            self._reset_reconnect_state()
            logger.info("WebSocket连接成功建立")
            return True
            
        except aiohttp.ClientError as e:
            logger.error(f"WebSocket连接失败: {str(e)}")
            if "401" in str(e):
                logger.info("尝试重新获取token并重连")
                self.token = None
                return await self.connect()
            return False
        except Exception as e:
            logger.error(f"建立WebSocket连接时发生异常: {str(e)}")
            return False

    async def reconnect(self) -> bool:
        """
        重新连接WebSocket
        :return: 是否成功重连
        """
        try:
            if self.ws:
                await self.ws.close()
            if self.session:
                await self.session.close()
            
            return await self.connect()
            
        except Exception as e:
            logger.error(f"WebSocket重连失败: {str(e)}")
            return False

    async def refresh_token(self) -> None:
        """刷新token"""
        try:
            logger.info("开始刷新token")
            if await self.get_token():
                logger.info("token刷新成功")
            else:
                logger.error("token刷新失败")
        except Exception as e:
            logger.error(f"刷新token时发生异常: {str(e)}")

    def register_message_handler(self, name: str, handler: Callable) -> None:
        """
        注册消息处理器
        :param name: 处理器名称
        :param handler: 处理器函数
        """
        self.message_handlers[name] = handler
        logger.info(f"注册消息处理器: {name}")

    def unregister_message_handler(self, name: str) -> None:
        """
        注销消息处理器
        :param name: 处理器名称
        """
        if name in self.message_handlers:
            del self.message_handlers[name]
            logger.info(f"注销消息处理器: {name}")

    async def _handle_message(self, message: str) -> None:
        """
        处理接收到的WebSocket消息
        :param message: 消息内容
        """
        try:
            # 更新最后消息时间
            self.last_message_time = time.time()
            
            data = json.loads(message)
            
            # 处理不同类型的消息
            if data.get("type") == "ping":
                await self._send_pong()
            else:
                # 处理无人机状态数据
                if data.get("biz_code") == "dock_osd":
                    await self._process_drone_status(data)
                
                # 调用注册的消息处理器
                for handler in self.message_handlers.values():
                    await handler(data)
                    
        except json.JSONDecodeError:
            logger.error(f"解析消息失败: {message}")
        except Exception as e:
            logger.error(f"处理消息时发生异常: {str(e)}")

    async def _process_drone_status(self, data: Dict[str, Any]) -> None:
        """
        处理无人机状态数据
        :param data: 无人机状态数据
        """
        try:
            if 'data' not in data or 'data' not in data['data']:
                return
                
            host_data = data['data']['data'].get('host', {})
            sn = data['data']['data'].get('sn')
            
            if not sn:
                return
                
            # 获取mode_code
            mode_code = host_data.get('mode_code')
            if mode_code is None:
                return
                
            # 获取device_sn
            sub_device = host_data.get('sub_device', {})
            device_sn = sub_device.get('device_sn')
            
            if not device_sn:
                return
                
            # 判断无人机是否活跃
            is_active = mode_code in config.WEBSOCKET_ACTIVE_MODE_CODES
            
            # 更新无人机状态 - 使用共享状态管理器
            from app.utils.shared_state import shared_state_manager
            status = {
                'device_sn': device_sn,
                'mode_code': mode_code,
                'is_active': is_active,
                'last_update': time.time(),
                'sn': sn
            }
            shared_state_manager.update_drone_status(device_sn, status)
            
            # logger.debug(f"更新无人机状态: {device_sn}, mode_code: {mode_code}, is_active: {is_active}")
            
        except Exception as e:
            logger.error(f"处理无人机状态数据异常: {str(e)}")

    def get_active_drone_ids(self) -> list:
        """
        获取当前活跃的无人机ID列表（使用统一状态管理）
        :return: 活跃无人机ID列表
        """
        from app.utils.shared_state import shared_state_manager
        # WebSocket 客户端通常不应用配置过滤，返回所有活跃的无人机
        return shared_state_manager.get_active_drone_ids(apply_config_filter=False)

    def is_drone_active(self, device_sn: str) -> bool:
        """
        检查指定无人机是否活跃（使用统一状态管理）
        :param device_sn: 无人机ID
        :return: 是否活跃
        """
        from app.utils.shared_state import shared_state_manager
        return shared_state_manager.is_drone_active(device_sn)

    async def _send_pong(self) -> None:
        """发送pong消息"""
        if self.ws and not self.ws.closed:
            try:
                await self.ws.send_json({"type": "pong", "timestamp": int(datetime.now().timestamp())})
            except Exception as e:
                logger.error(f"发送pong消息失败: {str(e)}")

    def setup_scheduler(self) -> None:
        """设置定时任务"""
        if not self.scheduler.running:
            # 设置token刷新任务
            hour = config.WEBSOCKET_TOKEN_REFRESH_HOUR
            minute = config.WEBSOCKET_TOKEN_REFRESH_MINUTE
            self.scheduler.add_job(
                self.refresh_token,
                CronTrigger(hour=hour, minute=minute),  # 每天指定时间刷新
                id='token_refresh'
            )
            self.scheduler.start()
            logger.info(f"已设置token刷新任务，刷新时间: {hour:02d}:{minute:02d}")

    async def start(self) -> None:
        """启动WebSocket客户端"""
        if self._running:
            return
            
        self._running = True
        self.setup_scheduler()
        
        while self._running:
            try:
                # 检查是否应该尝试重连
                if not self.ws and not self._should_attempt_reconnect():
                    # 夜间模式下的特殊处理
                    if self._is_night_mode():
                        await asyncio.sleep(self.night_mode_reconnect_interval)
                    else:
                        await asyncio.sleep(5)
                    continue
                
                # 尝试建立连接
                if not self.ws and not await self.connect():
                    self.reconnect_attempts += 1
                    self.consecutive_failures += 1
                    self.last_reconnect_time = time.time()
                    
                    delay = self._calculate_reconnect_delay()
                    logger.warning(f"WebSocket连接失败，第 {self.reconnect_attempts} 次重连，延迟 {delay:.1f} 秒")
                    await asyncio.sleep(delay)
                    continue
                
                # 连接成功，处理消息
                async for msg in self.ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        await self._handle_message(msg.data)
                    elif msg.type == aiohttp.WSMsgType.CLOSED:
                        break
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        break
                
                # 连接断开后的处理
                if self._running:
                    self.consecutive_failures += 1
                    logger.warning(f"WebSocket连接断开，连续失败次数: {self.consecutive_failures}")
                    
                    # 如果连续失败次数过多，重置token
                    if self.consecutive_failures >= self.max_consecutive_failures:
                        logger.warning("连续失败次数过多，重置token")
                        self.token = None
                        self.consecutive_failures = 0
                    
                    # 夜间模式下的特殊处理
                    if self._is_night_mode():
                        await asyncio.sleep(self.night_mode_reconnect_interval)
                    else:
                        await asyncio.sleep(5)
                    
            except Exception as e:
                logger.error(f"WebSocket处理异常: {str(e)}")
                self.consecutive_failures += 1
                await asyncio.sleep(5)

    async def stop(self) -> None:
        """停止WebSocket客户端"""
        if not self._running:
            return
            
        self._running = False
        logger.info("停止WebSocket客户端")
        
        try:
            # 停止调度器
            if self.scheduler.running:
                self.scheduler.shutdown()
                logger.info("WebSocket调度器已停止")
            
            # 关闭WebSocket连接
            if self.ws:
                try:
                    await self.ws.close()
                    logger.info("WebSocket连接已关闭")
                except Exception as e:
                    logger.error(f"关闭WebSocket连接时发生错误: {str(e)}")
                finally:
                    self.ws = None
            
            # 关闭aiohttp会话
            if self.session:
                try:
                    await self.session.close()
                    logger.info("aiohttp会话已关闭")
                except Exception as e:
                    logger.error(f"关闭aiohttp会话时发生错误: {str(e)}")
                finally:
                    self.session = None
                    
            logger.info("WebSocket客户端已完全停止")
            
        except Exception as e:
            logger.error(f"停止WebSocket客户端时发生错误: {str(e)}")
            import traceback
            logger.error(f"停止WebSocket客户端错误详情: {traceback.format_exc()}")
        finally:
            # 确保状态重置
            self._running = False 