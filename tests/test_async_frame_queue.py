import pytest
import asyncio
import numpy as np
import time
from unittest.mock import AsyncMock, MagicMock

# 添加项目根目录到路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.async_frame_queue import AsyncFrameQueue
from app.utils.frame_data import FrameData


class TestAsyncFrameQueue:
    """异步帧队列测试类"""
    
    @pytest.fixture
    def sample_frame_data(self):
        """创建示例帧数据"""
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        return FrameData(
            frame=frame,
            timestamp=time.time() * 1000,
            frame_id=1,
            metadata={"test": "data"}
        )
    
    @pytest.fixture
    def frame_queue(self):
        """创建帧队列实例"""
        return AsyncFrameQueue(max_size=10, drop_old_frames=True)
    
    @pytest.mark.asyncio
    async def test_put_and_get_frame(self, frame_queue, sample_frame_data):
        """测试帧的放入和获取"""
        # 放入帧
        success = await frame_queue.put_frame(sample_frame_data)
        assert success is True
        assert frame_queue.get_queue_size() == 1
        
        # 获取帧
        retrieved_frame = await frame_queue.get_frame(timeout=1.0)
        assert retrieved_frame is not None
        assert retrieved_frame.frame_id == sample_frame_data.frame_id
        assert frame_queue.get_queue_size() == 0
    
    @pytest.mark.asyncio
    async def test_timeout_on_empty_queue(self, frame_queue):
        """测试空队列超时"""
        retrieved_frame = await frame_queue.get_frame(timeout=0.1)
        assert retrieved_frame is None
    
    @pytest.mark.asyncio
    async def test_drop_old_frames(self, sample_frame_data):
        """测试丢弃旧帧功能"""
        frame_queue = AsyncFrameQueue(max_size=3, drop_old_frames=True)
        
        # 添加超过最大大小的帧
        for i in range(5):
            frame_data = FrameData(
                frame=np.zeros((100, 100, 3), dtype=np.uint8),
                timestamp=time.time() * 1000,
                frame_id=i,
                metadata={}
            )
            await frame_queue.put_frame(frame_data)
        
        # 队列大小应该保持在最大值
        assert frame_queue.get_queue_size() == 3
        
        # 获取的应该是最新的帧
        retrieved_frame = await frame_queue.get_frame()
        assert retrieved_frame.frame_id == 2  # 第一个保留的帧
    
    @pytest.mark.asyncio
    async def test_queue_statistics(self, frame_queue, sample_frame_data):
        """测试队列统计功能"""
        # 初始统计
        stats = frame_queue.get_stats()
        assert stats.total_frames == 0
        assert stats.dropped_frames == 0
        
        # 添加帧
        await frame_queue.put_frame(sample_frame_data)
        
        stats = frame_queue.get_stats()
        assert stats.queue_size == 1
    
    @pytest.mark.asyncio
    async def test_get_latest_frame(self, frame_queue):
        """测试获取最新帧功能"""
        # 添加多个帧
        for i in range(3):
            frame_data = FrameData(
                frame=np.zeros((100, 100, 3), dtype=np.uint8),
                timestamp=time.time() * 1000,
                frame_id=i,
                metadata={}
            )
            await frame_queue.put_frame(frame_data)
        
        # 获取最新帧
        latest_frame = await frame_queue.get_latest_frame()
        assert latest_frame is not None
        assert latest_frame.frame_id == 2  # 最后一个帧
        
        # 队列应该被清空
        assert frame_queue.get_queue_size() == 0
    
    @pytest.mark.asyncio
    async def test_close_queue(self, frame_queue, sample_frame_data):
        """测试关闭队列"""
        await frame_queue.put_frame(sample_frame_data)
        await frame_queue.close()
        
        # 关闭后无法放入或获取帧
        success = await frame_queue.put_frame(sample_frame_data)
        assert success is False
        
        retrieved_frame = await frame_queue.get_frame()
        assert retrieved_frame is None
    
    def test_queue_properties(self, frame_queue):
        """测试队列属性"""
        assert frame_queue.is_empty() is True
        assert frame_queue.is_full() is False
        assert len(frame_queue) == 0
    
    @pytest.mark.asyncio
    async def test_clear_queue(self, frame_queue, sample_frame_data):
        """测试清空队列"""
        # 添加几个帧
        for i in range(3):
            frame_data = FrameData(
                frame=np.zeros((100, 100, 3), dtype=np.uint8),
                timestamp=time.time() * 1000,
                frame_id=i,
                metadata={}
            )
            await frame_queue.put_frame(frame_data)
        
        # 清空队列
        cleared_count = frame_queue.clear()
        assert cleared_count == 3
        assert frame_queue.get_queue_size() == 0
        assert frame_queue.is_empty() is True


if __name__ == "__main__":
    pytest.main([__file__]) 