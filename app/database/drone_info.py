import mysql.connector
from typing import Dict, Optional, List
import logging
from dataclasses import dataclass
import time
import json
import os
from app.utils.config import config

logger = logging.getLogger(__name__)

@dataclass
class DroneInfo:
    """无人机信息数据类"""
    drone_code: str
    station_longitude: float  # 机场经度
    station_latitude: float   # 机场纬度
    drone_video_code: str
    airport_video_code: str
    drone_video_suffix: str
    airport_video_suffix: str
    sampling_point_longitude: float
    sampling_point_latitude: float
    longitude: float = 0.0    # 无人机实时经度
    latitude: float = 0.0     # 无人机实时纬度
    last_location_update_timestamp: float = 0.0  # 位置更新时间戳
    mode_code: int = 0        # 无人机模式代码
    
    @property
    def drone_rtmp_url(self) -> str:
        """获取无人机RTMP完整URL"""
        return f"{config.rtmp.base_url}{self.drone_video_code}-{self.drone_video_suffix}"

    @property
    def airport_rtmp_url(self) -> str:
        """获取机场RTMP完整URL"""
        return f"{config.rtmp.base_url}{self.airport_video_code}-{self.airport_video_suffix}"

class DroneInfoManager:
    """无人机信息管理器，负责管理无人机基础信息、位置信息和活跃状态"""
    
    # 单例实例
    _instance = None
    
    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(DroneInfoManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化管理器，延迟加载无人机基础信息"""
        # 确保只初始化一次
        if self._initialized:
            return
            
        self.db_config = config.database
        self._db_connection = None
        self.drone_info_cache: Dict[str, DroneInfo] = {}
        self.stream_monitor = None  # 用于与流监控器交互
        
        # 标记为已初始化
        self._initialized = True

    # 状态持久化相关方法已移除，不再需要

    def _fix_coordinate_format(self, coord_str: str) -> float:
        """
        修复坐标格式，处理空格替代小数点的情况
        :param coord_str: 坐标字符串
        :return: 修复后的浮点数坐标
        """
        try:
            if isinstance(coord_str, (int, float)):
                return float(coord_str)
            
            # 如果是字符串，需要处理格式问题
            coord_str = str(coord_str).strip()
            
            # 处理空格替代小数点的情况
            # 例如：'121 120772' -> '121.120772'
            if ' ' in coord_str and '.' not in coord_str:
                # 找到第一个空格的位置
                space_index = coord_str.find(' ')
                # 将空格替换为小数点
                coord_str = coord_str[:space_index] + '.' + coord_str[space_index+1:]
                logger.debug(f"修复坐标格式: 原始值包含空格，已转换为 {coord_str}")
            
            return float(coord_str)
        except (ValueError, TypeError) as e:
            logger.error(f"坐标格式修复失败: {coord_str}, 错误: {str(e)}")
            return 0.0

    def _is_drone_in_config_list(self, drone_code: str) -> bool:
        """
        检查无人机是否在配置列表中（根据配置决定是否启用过滤）
        :param drone_code: 无人机编号
        :return: 是否在配置列表中
        """
        # 如果配置为只监控配置文件中的无人机，则检查列表
        if config.stream_monitor.only_monitor_config_drones:
            config_drone_codes = config.get_drone_codes()
            return drone_code in config_drone_codes
        # 否则接受所有无人机
        return True

    def _load_drone_info(self, drone_code: str) -> Optional[DroneInfo]:
        """
        从数据库加载指定无人机的基础信息
        :param drone_code: 无人机编号
        :return: 无人机信息对象，如果不存在则返回None
        """
        try:
            conn = self._get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            query = """
                SELECT drone_code, longitude as station_longitude, latitude as station_latitude, 
                       drone_video_code, airport_video_code, drone_video_suffix, airport_video_suffix,
                       sampling_point_longitude, sampling_point_latitude
                FROM drone_info 
                WHERE drone_code = %s
            """
            cursor.execute(query, (drone_code,))
            result = cursor.fetchone()
            logger.debug(f"加载无人机 {drone_code} 的基础信息: {result}")
            
            if result:
                # 使用修复方法处理经纬度数据
                drone_info = DroneInfo(
                    drone_code=result['drone_code'],
                    station_longitude=self._fix_coordinate_format(result['station_longitude']),
                    station_latitude=self._fix_coordinate_format(result['station_latitude']),
                    drone_video_code=result['drone_video_code'],
                    airport_video_code=result['airport_video_code'],
                    drone_video_suffix=result['drone_video_suffix'],
                    airport_video_suffix=result['airport_video_suffix'],
                    sampling_point_longitude=self._fix_coordinate_format(result['sampling_point_longitude']),
                    sampling_point_latitude=self._fix_coordinate_format(result['sampling_point_latitude'])
                )
                
                self.drone_info_cache[drone_code] = drone_info
                logger.debug(f"成功加载无人机 {drone_code} 的基础信息")
                return drone_info
            else:
                logger.warning(f"未找到无人机 {drone_code} 的基础信息")
                return None
                
        except Exception as e:
            logger.error(f"加载无人机 {drone_code} 基础信息失败: {str(e)}")
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()

    def get_drone_info(self, drone_code: str) -> Optional[DroneInfo]:
        """获取无人机信息，如果缓存中没有则按需加载"""
        # 如果缓存中没有，尝试从数据库加载
        if drone_code not in self.drone_info_cache:
            return self._load_drone_info(drone_code)
        return self.drone_info_cache.get(drone_code)
            
    def set_stream_monitor(self, monitor):
        """设置流监控器"""
        self.stream_monitor = monitor
        
    def get_active_drones(self) -> List[str]:
        """
        获取当前活跃的无人机列表（使用统一状态管理）
        :return: 活跃无人机的编号列表
        """
        from app.utils.shared_state import shared_state_manager
        # 这里应用配置过滤，因为 DroneInfoManager 通常用于业务逻辑
        active_drone_ids = shared_state_manager.get_active_drone_ids(apply_config_filter=True)
        logger.debug(f"从统一状态管理获取活跃无人机列表: {active_drone_ids}")
        return active_drone_ids

    async def handle_websocket_data(self, data: dict) -> None:
        """
        处理WebSocket接收到的无人机数据，更新无人机位置和活跃状态
        :param data: WebSocket消息
        """
        try:
            if data.get("biz_code") != "device_osd":
                return
                
            device_data = data.get("data", {})
            drone_code = device_data.get("sn")
            if not drone_code:
                logger.warning("收到的WebSocket消息中没有无人机编号")
                return
                
            # 检查无人机是否在配置列表中
            if not self._is_drone_in_config_list(drone_code):
                # logger.debug(f"无人机 {drone_code} 不在配置列表中，跳过处理")
                return
                
            # 获取位置数据和模式代码
            the_drone_data = device_data.get("host", {})
            longitude = the_drone_data.get("longitude")
            latitude = the_drone_data.get("latitude")
            mode_code = the_drone_data.get("mode_code")
            
            # 统一状态更新逻辑
            await self._update_drone_status(drone_code, mode_code, longitude, latitude)
            
        except Exception as e:
            logger.error(f"处理WebSocket数据失败: {str(e)}")

    async def _update_drone_status(self, drone_code: str, mode_code: Optional[int], 
                                 longitude: Optional[float], latitude: Optional[float]) -> None:
        """
        统一更新无人机状态
        :param drone_code: 无人机编号
        :param mode_code: 模式代码
        :param longitude: 经度
        :param latitude: 纬度
        """
        try:
            from app.utils.shared_state import shared_state_manager
            current_time = time.time()
            
            # 检查无人机是否在配置列表中
            if not self._is_drone_in_config_list(drone_code):
                # logger.debug(f"无人机 {drone_code} 不在配置列表中，跳过状态更新")
                return
            
            # 获取或创建无人机状态（不应用配置过滤，因为我们需要检查原始状态）
            from app.utils.shared_state import shared_state_manager
            active_drones = shared_state_manager.get_active_drones(apply_config_filter=False)
            if drone_code not in active_drones:
                status = {
                    'device_sn': drone_code,
                    'mode_code': 0,
                    'is_active': False,
                    'last_update': current_time
                }
                shared_state_manager.update_drone_status(drone_code, status)
            
            # 更新模式代码和活跃状态
            if mode_code is not None:
                mode_code = int(mode_code)
                is_active = mode_code in config.WEBSOCKET_ACTIVE_MODE_CODES
                
                status = {
                    'device_sn': drone_code,
                    'mode_code': mode_code,
                    'is_active': is_active,
                    'last_update': current_time
                }
                
                # 如果有位置信息，也更新到状态中
                if longitude is not None and latitude is not None:
                    status['longitude'] = float(longitude)
                    status['latitude'] = float(latitude)
                
                shared_state_manager.update_drone_status(drone_code, status)
                
                # 如果无人机变为活跃状态，确保加载其基础信息
                if is_active and drone_code not in self.drone_info_cache:
                    logger.info(f"无人机 {drone_code} 变为活跃状态，加载基础信息")
                    self._load_drone_info(drone_code)
            
            # 更新位置信息
            if longitude is not None and latitude is not None:
                # 同时更新缓存中的位置信息
                drone_info = self.get_drone_info(drone_code)
                if drone_info:
                    drone_info.longitude = float(longitude)
                    drone_info.latitude = float(latitude)
                    drone_info.last_location_update_timestamp = current_time
                    if mode_code is not None:
                        drone_info.mode_code = mode_code
            
            # 状态清理和保存逻辑已移除，改为在WebSocket客户端进行实时有效性检查
                
        except Exception as e:
            logger.error(f"更新无人机状态失败: {str(e)}")

    def _get_db_connection(self):
        """获取数据库连接"""
        if not self._db_connection or not self._db_connection.is_connected():
            self._db_connection = mysql.connector.connect(
                host=self.db_config.host,
                port=self.db_config.port,
                user=self.db_config.user,
                password=self.db_config.password,
                database=self.db_config.database,
                auth_plugin='mysql_native_password'
            )
        return self._db_connection

    def close(self):
        """关闭数据库连接"""
        if self._db_connection and self._db_connection.is_connected():
            self._db_connection.close()

    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两个坐标点之间的距离（米）"""
        from math import radians, sin, cos, sqrt, atan2
        
        R = 6371000  # 地球半径（米）
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return R * c

    def check_target_distance(self, drone_info: DroneInfo, target_config: dict) -> bool:
        """
        检查无人机是否在目标检测范围内
        :param drone_info: 无人机信息
        :param target_config: 目标配置
        :return: 是否在检测范围内
        """
        try:
            # 获取目标距离
            target_distance = target_config.get('distance', 50)
            
            # 根据目标类型选择参考点
            if target_config.get('type') == 'sampling_point':
                ref_lat = drone_info.sampling_point_latitude
                ref_lon = drone_info.sampling_point_longitude
            else:  # drone_location
                ref_lat = drone_info.latitude
                ref_lon = drone_info.longitude
            
            # 计算距离
            distance = self.calculate_distance(
                drone_info.latitude, drone_info.longitude,
                ref_lat, ref_lon
            )
            
            return distance <= target_distance
            
        except Exception as e:
            logger.error(f"检查目标距离失败: {str(e)}")
            return False 